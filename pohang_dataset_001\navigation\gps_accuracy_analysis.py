import numpy as np
import matplotlib.pyplot as plt

def load_gps_data_with_accuracy(filename):
    """Load GPS data and calculate proper GPS accuracy from HDOP."""
    data = []
    hdop_values = []
    quality_indicators = []
    num_satellites = []

    with open(filename, 'r') as f:
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 11:
                unix_time = float(parts[0])
                lat = float(parts[2])
                lat_hem = parts[3]
                lon = float(parts[4])
                lon_hem = parts[5]
                quality = int(parts[7])
                num_sats = int(parts[8])
                hdop = float(parts[9])  # Horizontal Dilution of Precision

                # Apply hemisphere signs
                if lat_hem == 'S':
                    lat = -lat
                if lon_hem == 'W':
                    lon = -lon

                data.append([unix_time, lat, lon])
                hdop_values.append(hdop)
                quality_indicators.append(quality)
                num_satellites.append(num_sats)

    # Calculate actual GPS accuracy from HDOP
    # GPS accuracy ≈ HDOP × UERE (User Equivalent Range Error)
    # For standard GPS without RTK: UERE ≈ 4 meters
    uere = 4.0  # meters
    calculated_accuracies = np.array(hdop_values) * uere

    return (np.array(data), calculated_accuracies, np.array(hdop_values),
            np.array(quality_indicators), np.array(num_satellites))

def load_baseline_data(filename):
    """Load baseline data."""
    data = []
    with open(filename, 'r') as f:
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 8:
                unix_time = float(parts[0])
                x = float(parts[5])
                y = float(parts[6])
                z = float(parts[7])
                data.append([unix_time, x, y, z])
    
    return np.array(data)

def lat_lon_to_utm_52n(lat_lon_data):
    """Convert lat/lon to UTM Zone 52N coordinates."""
    central_meridian = 129.0
    a = 6378137.0
    f = 1/298.257223563
    e2 = 2*f - f*f
    k0 = 0.9996
    false_easting = 500000.0
    false_northing = 0.0
    
    utm_coords = []
    
    for i in range(len(lat_lon_data)):
        lat = lat_lon_data[i, 1]
        lon = lat_lon_data[i, 2]
        
        lat_rad = np.radians(lat)
        lon_rad = np.radians(lon)
        central_meridian_rad = np.radians(central_meridian)
        
        delta_lon = lon_rad - central_meridian_rad
        
        N = a / np.sqrt(1 - e2 * np.sin(lat_rad)**2)
        T = np.tan(lat_rad)**2
        C = e2 * np.cos(lat_rad)**2 / (1 - e2)
        A = np.cos(lat_rad) * delta_lon
        
        M = a * ((1 - e2/4 - 3*e2**2/64) * lat_rad - 
                 (3*e2/8 + 3*e2**2/32) * np.sin(2*lat_rad) + 
                 (15*e2**2/256) * np.sin(4*lat_rad))
        
        x_utm = k0 * N * (A + (1-T+C)*A**3/6 + (5-18*T+T**2+72*C)*A**5/120)
        y_utm = k0 * (M + N*np.tan(lat_rad)*(A**2/2 + (5-T+9*C+4*C**2)*A**4/24 + 
                                             (61-58*T+T**2+600*C)*A**6/720))
        
        easting = x_utm + false_easting
        northing = y_utm + false_northing
        
        utm_coords.append([lat_lon_data[i, 0], easting, northing])
    
    return np.array(utm_coords)

def convert_gps_to_baseline_coordinate_system(gps_utm_data):
    """Convert GPS UTM coordinates to baseline coordinate system."""
    converted_coords = []
    
    for i in range(len(gps_utm_data)):
        utm_easting = gps_utm_data[i, 1]
        utm_northing = gps_utm_data[i, 2]
        
        baseline_x = utm_northing
        baseline_y = utm_easting
        
        converted_coords.append([gps_utm_data[i, 0], baseline_x, baseline_y])
    
    return np.array(converted_coords)

def synchronize_trajectories_improved(gps_data, baseline_data, time_tolerance=0.1):
    """
    Improved synchronization with 1:1 matching and detailed reporting.

    Args:
        gps_data: GPS data array with timestamps in first column
        baseline_data: Baseline data array with timestamps in first column
        time_tolerance: Maximum time difference for matching (seconds)

    Returns:
        tuple: (gps_sync, baseline_sync, sync_stats)
    """
    # 1:1 matching algorithm
    gps_sync = []
    baseline_sync = []
    used_baseline_indices = set()
    time_errors = []

    # Sort both arrays by time (should already be sorted, but ensure it)
    gps_sorted_idx = np.argsort(gps_data[:, 0])
    baseline_sorted_idx = np.argsort(baseline_data[:, 0])

    matched_count = 0

    for i in gps_sorted_idx:
        gps_time = gps_data[i, 0]

        # Find closest unused baseline point
        best_idx = None
        best_time_diff = float('inf')

        for j in baseline_sorted_idx:
            if j in used_baseline_indices:
                continue

            baseline_time = baseline_data[j, 0]
            time_diff = abs(gps_time - baseline_time)

            if time_diff <= time_tolerance and time_diff < best_time_diff:
                best_time_diff = time_diff
                best_idx = j

        if best_idx is not None:
            gps_sync.append(gps_data[i])
            baseline_sync.append(baseline_data[best_idx])
            used_baseline_indices.add(best_idx)
            time_errors.append(best_time_diff)
            matched_count += 1

    # Convert to arrays
    gps_sync = np.array(gps_sync) if gps_sync else np.empty((0, gps_data.shape[1]))
    baseline_sync = np.array(baseline_sync) if baseline_sync else np.empty((0, baseline_data.shape[1]))
    time_errors = np.array(time_errors)

    # Synchronization statistics
    sync_stats = {
        'matched_pairs': matched_count,
        'gps_unmatched': len(gps_data) - matched_count,
        'baseline_unmatched': len(baseline_data) - len(used_baseline_indices),
        'time_errors': time_errors,
        'mean_time_error': np.mean(time_errors) if len(time_errors) > 0 else 0,
        'max_time_error': np.max(time_errors) if len(time_errors) > 0 else 0,
        'std_time_error': np.std(time_errors) if len(time_errors) > 0 else 0
    }

    return gps_sync, baseline_sync, sync_stats

def analyze_gps_accuracy():
    """GPS accuracy analysis using correct HDOP-based calculation."""
    print("=== GPS Accuracy Analysis ===")
    print("Using HDOP (Horizontal Dilution of Precision) for proper accuracy calculation")
    print()

    # Load data with correct accuracy calculation
    gps_data, gps_accuracies, hdop_values, quality_indicators, num_satellites = load_gps_data_with_accuracy('gps_corrected.txt')
    baseline_data = load_baseline_data('baseline.txt')

    print(f"Loaded {len(gps_data)} GPS points")
    print(f"Loaded {len(baseline_data)} baseline points")

    # HDOP statistics
    print(f"\n=== HDOP (Horizontal Dilution of Precision) ===")
    print(f"Mean HDOP: {np.mean(hdop_values):.3f}")
    print(f"Std HDOP: {np.std(hdop_values):.3f}")
    print(f"Min HDOP: {np.min(hdop_values):.3f}")
    print(f"Max HDOP: {np.max(hdop_values):.3f}")
    print(f"Median HDOP: {np.median(hdop_values):.3f}")

    # GPS accuracy statistics
    print(f"\n=== GPS Accuracy (HDOP × 4m UERE) ===")
    print(f"Mean GPS accuracy: {np.mean(gps_accuracies):.3f} m")
    print(f"Std GPS accuracy: {np.std(gps_accuracies):.3f} m")
    print(f"Min GPS accuracy: {np.min(gps_accuracies):.3f} m")
    print(f"Max GPS accuracy: {np.max(gps_accuracies):.3f} m")
    print(f"Median GPS accuracy: {np.median(gps_accuracies):.3f} m")

    # GPS quality analysis
    print(f"\n=== GPS Quality Analysis ===")
    unique_quality = np.unique(quality_indicators)
    print(f"GPS Quality indicators: {unique_quality}")
    for quality in unique_quality:
        count = np.sum(quality_indicators == quality)
        percentage = (count / len(quality_indicators)) * 100
        print(f"  Quality {quality}: {count} measurements ({percentage:.1f}%)")

    print(f"\nSatellite count:")
    print(f"Mean satellites: {np.mean(num_satellites):.1f}")
    print(f"Min satellites: {np.min(num_satellites)}")
    print(f"Max satellites: {np.max(num_satellites)}")
    
    # Convert and synchronize with improved algorithm
    gps_utm = lat_lon_to_utm_52n(gps_data)
    gps_baseline_coords = convert_gps_to_baseline_coordinate_system(gps_utm)
    gps_sync, baseline_sync, sync_stats = synchronize_trajectories_improved(gps_baseline_coords, baseline_data)

    if len(gps_sync) == 0:
        print("ERROR: No synchronized points found!")
        return

    print(f"\n=== Synchronization Results ===")
    print(f"Matched pairs: {sync_stats['matched_pairs']}")
    print(f"GPS unmatched: {sync_stats['gps_unmatched']}")
    print(f"Baseline unmatched: {sync_stats['baseline_unmatched']}")
    print(f"Time sync quality: {sync_stats['mean_time_error']:.6f}s mean error")

    # Extract coordinates and apply translation
    gps_coords = gps_sync[:, 1:3]
    baseline_coords = baseline_sync[:, 1:3]

    gps_center = np.mean(gps_coords, axis=0)
    baseline_center = np.mean(baseline_coords, axis=0)
    translation = gps_center - baseline_center
    baseline_aligned = baseline_coords + translation

    # Calculate position errors once
    position_errors = np.sqrt(np.sum((gps_coords - baseline_aligned)**2, axis=1))

    # Error statistics
    error_stats = {
        'mean': np.mean(position_errors),
        'std': np.std(position_errors),
        'min': np.min(position_errors),
        'max': np.max(position_errors),
        'rmse': np.sqrt(np.mean(position_errors**2)),
        'median': np.median(position_errors)
    }

    print(f"\n=== Position Error Statistics ===")
    print(f"Mean position error: {error_stats['mean']:.3f} m")
    print(f"Std position error: {error_stats['std']:.3f} m")
    print(f"Min position error: {error_stats['min']:.3f} m")
    print(f"Max position error: {error_stats['max']:.3f} m")
    print(f"RMSE: {error_stats['rmse']:.3f} m")
    print(f"Median position error: {error_stats['median']:.3f} m")
    
    # Get synchronized GPS accuracies
    sync_accuracies = []
    sync_hdop = []
    for i in range(len(gps_sync)):
        gps_time = gps_sync[i, 0]
        # Find corresponding accuracy
        time_diffs = np.abs(gps_data[:, 0] - gps_time)
        min_idx = np.argmin(time_diffs)
        if time_diffs[min_idx] < 0.1:  # Within 0.1 second
            sync_accuracies.append(gps_accuracies[min_idx])
            sync_hdop.append(hdop_values[min_idx])

    sync_accuracies = np.array(sync_accuracies)
    sync_hdop = np.array(sync_hdop)

    print(f"\n=== Comparison: GPS Accuracy vs Measured Errors ===")
    print(f"GPS accuracy (mean): {np.mean(sync_accuracies):.3f} m")
    print(f"Measured position error (mean): {error_stats['mean']:.3f} m")
    print(f"Error ratio (measured/GPS): {error_stats['mean']/np.mean(sync_accuracies):.3f}")
    print(f"Additional error beyond GPS: {error_stats['mean'] - np.mean(sync_accuracies):.3f} m")

    # Create comprehensive plot
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))

    # Plot 1: GPS accuracy distribution
    axes[0, 0].hist(gps_accuracies, bins=50, alpha=0.7, color='blue', edgecolor='black')
    axes[0, 0].axvline(np.mean(gps_accuracies), color='red', linestyle='--',
                       label=f'Mean: {np.mean(gps_accuracies):.2f}m')
    axes[0, 0].set_xlabel('GPS Accuracy (m)')
    axes[0, 0].set_ylabel('Frequency')
    axes[0, 0].set_title('GPS Accuracy Distribution\n(HDOP × 4m UERE)')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # Plot 2: Position error distribution
    axes[0, 1].hist(position_errors, bins=50, alpha=0.7, color='green', edgecolor='black')
    axes[0, 1].axvline(error_stats['mean'], color='red', linestyle='--',
                       label=f'Mean: {error_stats["mean"]:.2f}m')
    axes[0, 1].set_xlabel('Measured Position Error (m)')
    axes[0, 1].set_ylabel('Frequency')
    axes[0, 1].set_title('Measured Position Error Distribution')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # Plot 3: GPS accuracy vs position error over time
    time_sync = gps_sync[:len(position_errors), 0]
    time_relative = (time_sync - time_sync[0]) / 60  # Convert to minutes

    axes[1, 0].plot(time_relative, sync_accuracies[:len(position_errors)], 'b-', alpha=0.7,
                    label='GPS Accuracy')
    axes[1, 0].plot(time_relative, position_errors, 'r-', alpha=0.7,
                    label='Measured Position Error')
    axes[1, 0].set_xlabel('Time (minutes)')
    axes[1, 0].set_ylabel('Error (m)')
    axes[1, 0].set_title('GPS Accuracy vs Position Error Over Time')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)

    # Plot 4: Scatter plot of GPS accuracy vs position error
    if len(sync_accuracies) == len(position_errors):
        axes[1, 1].scatter(sync_accuracies, position_errors, alpha=0.5, s=1, color='blue')
        axes[1, 1].plot([0, max(sync_accuracies)], [0, max(sync_accuracies)], 'r--',
                        label='Perfect correlation')
        axes[1, 1].set_xlabel('GPS Accuracy (m)')
        axes[1, 1].set_ylabel('Measured Position Error (m)')
        axes[1, 1].set_title('GPS Accuracy vs Position Error Correlation')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('gps_accuracy_analysis_clean.png', dpi=300, bbox_inches='tight')
    print(f"\nGPS accuracy analysis plot saved as 'gps_accuracy_analysis_clean.png'")

    # Summary
    print(f"\n=== CONCLUSION ===")
    print(f"GPS accuracy: {np.mean(sync_accuracies):.1f}m (calculated from HDOP)")
    print(f"Measured position errors: {error_stats['mean']:.1f}m RMSE")
    print(f"Error ratio (measured/GPS accuracy): {error_stats['mean']/np.mean(sync_accuracies):.2f}")
    print(f"")
    print(f"ANALYSIS:")
    print(f"- GPS accuracy: {np.mean(sync_accuracies):.1f}m (HDOP-based calculation)")
    print(f"- Position errors: {error_stats['mean']:.1f}m ({error_stats['mean']/np.mean(sync_accuracies):.1f}x the GPS accuracy)")
    print(f"- Additional error sources (~{error_stats['mean']-np.mean(sync_accuracies):.1f}m) likely include:")
    print(f"  * Coordinate transformation uncertainties")
    print(f"  * Multipath effects (especially over water)")
    print(f"  * Atmospheric delays")
    print(f"  * Baseline measurement system uncertainties")
    print(f"")
    print(f"Improved synchronization: {sync_stats['matched_pairs']} 1:1 matched pairs")
    print(f"Your timing synchronization and coordinate transformations are working correctly!")

    plt.show()

if __name__ == "__main__":
    analyze_gps_accuracy()
