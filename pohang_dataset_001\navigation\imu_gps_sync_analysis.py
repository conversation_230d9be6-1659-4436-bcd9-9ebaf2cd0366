import numpy as np
import matplotlib.pyplot as plt

def load_imu_data(filename):
    """Load IMU (AHRS) data."""
    data = []
    with open(filename, 'r') as f:
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 11:
                unix_time = float(parts[0])
                # Quaternion: qx, qy, qz, qw
                qx, qy, qz, qw = float(parts[1]), float(parts[2]), float(parts[3]), float(parts[4])
                # Angular rates: wx, wy, wz (rad/s)
                wx, wy, wz = float(parts[5]), float(parts[6]), float(parts[7])
                # Accelerations: ax, ay, az (m/s²)
                ax, ay, az = float(parts[8]), float(parts[9]), float(parts[10])
                
                data.append([unix_time, qx, qy, qz, qw, wx, wy, wz, ax, ay, az])
    
    return np.array(data)

def load_gps_data(filename):
    """Load GPS data."""
    data = []
    with open(filename, 'r') as f:
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 11:
                unix_time = float(parts[0])
                gps_time = float(parts[1])
                lat = float(parts[2])
                lat_hem = parts[3]
                lon = float(parts[4])
                lon_hem = parts[5]
                heading = float(parts[6])
                quality = int(parts[7])
                num_sats = int(parts[8])
                hdop = float(parts[9])
                geoid_height = float(parts[10])
                
                # Apply hemisphere signs
                if lat_hem == 'S':
                    lat = -lat
                if lon_hem == 'W':
                    lon = -lon
                
                data.append([unix_time, gps_time, lat, lon, heading, quality, num_sats, hdop, geoid_height])
    
    return np.array(data)

def analyze_time_synchronization(imu_data, gps_data):
    """Analyze time synchronization between IMU and GPS data."""
    print("=== IMU-GPS TIME SYNCHRONIZATION ANALYSIS ===")
    
    imu_times = imu_data[:, 0]
    gps_times = gps_data[:, 0]
    
    print(f"\nData Overview:")
    print(f"IMU samples: {len(imu_data):,}")
    print(f"GPS samples: {len(gps_data):,}")
    
    # Time range analysis
    imu_start, imu_end = imu_times[0], imu_times[-1]
    gps_start, gps_end = gps_times[0], gps_times[-1]
    
    print(f"\nTime Ranges:")
    print(f"IMU: {imu_start:.6f} to {imu_end:.6f} ({imu_end - imu_start:.1f} seconds)")
    print(f"GPS: {gps_start:.6f} to {gps_end:.6f} ({gps_end - gps_start:.1f} seconds)")
    
    # Time offset analysis
    time_offset = gps_start - imu_start
    print(f"\nTime Offset (GPS - IMU start): {time_offset:.6f} seconds")
    
    # Sampling rate analysis
    imu_dt = np.diff(imu_times)
    gps_dt = np.diff(gps_times)
    
    print(f"\nSampling Rates:")
    print(f"IMU mean interval: {np.mean(imu_dt):.6f}s ({1/np.mean(imu_dt):.1f} Hz)")
    print(f"IMU std interval: {np.std(imu_dt):.6f}s")
    print(f"GPS mean interval: {np.mean(gps_dt):.6f}s ({1/np.mean(gps_dt):.1f} Hz)")
    print(f"GPS std interval: {np.std(gps_dt):.6f}s")
    
    # Find overlapping time period
    overlap_start = max(imu_start, gps_start)
    overlap_end = min(imu_end, gps_end)
    overlap_duration = overlap_end - overlap_start
    
    print(f"\nOverlapping Period:")
    print(f"Start: {overlap_start:.6f}")
    print(f"End: {overlap_end:.6f}")
    print(f"Duration: {overlap_duration:.1f} seconds")
    
    if overlap_duration <= 0:
        print("❌ ERROR: No overlapping time period found!")
        return None
    
    # Extract overlapping data
    imu_mask = (imu_times >= overlap_start) & (imu_times <= overlap_end)
    gps_mask = (gps_times >= overlap_start) & (gps_times <= overlap_end)
    
    imu_overlap = imu_data[imu_mask]
    gps_overlap = gps_data[gps_mask]
    
    print(f"\nOverlapping Samples:")
    print(f"IMU: {len(imu_overlap):,} samples")
    print(f"GPS: {len(gps_overlap):,} samples")
    
    # Time synchronization quality check
    print(f"\n=== TIME SYNCHRONIZATION QUALITY ===")
    
    # Check for time gaps
    imu_gaps = imu_dt[imu_dt > 0.1]  # Gaps > 100ms
    gps_gaps = gps_dt[gps_dt > 1.0]  # Gaps > 1s
    
    print(f"Large time gaps:")
    print(f"IMU gaps > 100ms: {len(imu_gaps)} (max: {np.max(imu_gaps) if len(imu_gaps) > 0 else 0:.3f}s)")
    print(f"GPS gaps > 1s: {len(gps_gaps)} (max: {np.max(gps_gaps) if len(gps_gaps) > 0 else 0:.3f}s)")
    
    # Check for timestamp monotonicity
    imu_monotonic = np.all(np.diff(imu_times) > 0)
    gps_monotonic = np.all(np.diff(gps_times) > 0)
    
    print(f"\nTimestamp monotonicity:")
    print(f"IMU timestamps monotonic: {'✅' if imu_monotonic else '❌'}")
    print(f"GPS timestamps monotonic: {'✅' if gps_monotonic else '❌'}")
    
    # Check for duplicate timestamps
    imu_duplicates = len(imu_times) - len(np.unique(imu_times))
    gps_duplicates = len(gps_times) - len(np.unique(gps_times))
    
    print(f"\nDuplicate timestamps:")
    print(f"IMU duplicates: {imu_duplicates}")
    print(f"GPS duplicates: {gps_duplicates}")
    
    return {
        'imu_data': imu_overlap,
        'gps_data': gps_overlap,
        'time_offset': time_offset,
        'overlap_duration': overlap_duration,
        'imu_rate': 1/np.mean(imu_dt),
        'gps_rate': 1/np.mean(gps_dt),
        'imu_monotonic': imu_monotonic,
        'gps_monotonic': gps_monotonic,
        'sync_quality': 'Good' if imu_monotonic and gps_monotonic and overlap_duration > 0 else 'Issues detected'
    }

def plot_synchronization_analysis(imu_data, gps_data, sync_results):
    """Create plots to visualize time synchronization."""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    imu_times = imu_data[:, 0]
    gps_times = gps_data[:, 0]
    
    # Plot 1: Timeline comparison
    axes[0, 0].plot(imu_times - imu_times[0], np.ones(len(imu_times)), 'b.', alpha=0.1, markersize=1, label='IMU')
    axes[0, 0].plot(gps_times - gps_times[0], np.ones(len(gps_times)) * 1.1, 'r.', alpha=0.5, markersize=2, label='GPS')
    axes[0, 0].set_xlabel('Time (seconds from start)')
    axes[0, 0].set_ylabel('Data Stream')
    axes[0, 0].set_title('Timeline Comparison')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # Plot 2: Sampling intervals
    imu_dt = np.diff(imu_times)
    gps_dt = np.diff(gps_times)
    
    axes[0, 1].hist(imu_dt, bins=50, alpha=0.7, label=f'IMU (mean: {np.mean(imu_dt):.4f}s)', color='blue')
    axes[0, 1].hist(gps_dt, bins=50, alpha=0.7, label=f'GPS (mean: {np.mean(gps_dt):.4f}s)', color='red')
    axes[0, 1].set_xlabel('Time Interval (seconds)')
    axes[0, 1].set_ylabel('Frequency')
    axes[0, 1].set_title('Sampling Interval Distribution')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # Plot 3: Time series overlay (first 1000 samples)
    n_samples = min(1000, len(imu_times), len(gps_times))
    axes[1, 0].plot(imu_times[:n_samples] - imu_times[0], label='IMU', alpha=0.7)
    axes[1, 0].plot(gps_times[:n_samples] - gps_times[0], label='GPS', alpha=0.7)
    axes[1, 0].set_xlabel('Sample Index')
    axes[1, 0].set_ylabel('Time (seconds from start)')
    axes[1, 0].set_title('Time Series Overlay (First 1000 samples)')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # Plot 4: Time difference analysis
    if sync_results and len(sync_results['imu_data']) > 0 and len(sync_results['gps_data']) > 0:
        # Find nearest GPS time for each IMU sample in overlap period
        imu_overlap_times = sync_results['imu_data'][:, 0]
        gps_overlap_times = sync_results['gps_data'][:, 0]
        
        # Sample every 100th point for performance
        sample_indices = np.arange(0, len(imu_overlap_times), 100)
        time_diffs = []
        
        for i in sample_indices:
            imu_time = imu_overlap_times[i]
            closest_gps_idx = np.argmin(np.abs(gps_overlap_times - imu_time))
            time_diff = gps_overlap_times[closest_gps_idx] - imu_time
            time_diffs.append(time_diff)
        
        axes[1, 1].plot(time_diffs, 'g-', alpha=0.7)
        axes[1, 1].set_xlabel('Sample Index (every 100th)')
        axes[1, 1].set_ylabel('Time Difference (GPS - IMU) [s]')
        axes[1, 1].set_title('Time Synchronization Drift')
        axes[1, 1].grid(True, alpha=0.3)
        
        mean_diff = np.mean(time_diffs)
        std_diff = np.std(time_diffs)
        axes[1, 1].axhline(mean_diff, color='red', linestyle='--', label=f'Mean: {mean_diff:.6f}s')
        axes[1, 1].legend()
    
    plt.tight_layout()
    plt.savefig('imu_gps_synchronization_analysis.png', dpi=300, bbox_inches='tight')
    print(f"\n✅ Synchronization analysis plot saved as 'imu_gps_synchronization_analysis.png'")
    
    return fig

def main():
    """Main analysis function."""
    print("Loading IMU and GPS data...")
    
    # Load data
    imu_data = load_imu_data('ahrs.txt')
    gps_data = load_gps_data('gps.txt')
    
    print(f"Loaded {len(imu_data):,} IMU samples and {len(gps_data):,} GPS samples")
    
    # Analyze synchronization
    sync_results = analyze_time_synchronization(imu_data, gps_data)
    
    if sync_results:
        # Create visualization
        plot_synchronization_analysis(imu_data, gps_data, sync_results)
        
        # Summary and recommendations
        print(f"\n=== SUMMARY AND RECOMMENDATIONS ===")
        print(f"Synchronization Quality: {sync_results['sync_quality']}")
        print(f"Time Offset: {sync_results['time_offset']:.6f} seconds")
        print(f"IMU Rate: {sync_results['imu_rate']:.1f} Hz")
        print(f"GPS Rate: {sync_results['gps_rate']:.1f} Hz")
        
        if abs(sync_results['time_offset']) > 0.1:
            print(f"⚠️  WARNING: Significant time offset detected ({sync_results['time_offset']:.3f}s)")
            print(f"   Consider applying time offset correction in your INS/GPS fusion")
        
        if sync_results['imu_rate'] < 50:
            print(f"⚠️  WARNING: Low IMU rate ({sync_results['imu_rate']:.1f} Hz)")
            print(f"   Consider higher rate IMU data for better INS performance")
        
        print(f"\n📋 RECOMMENDATIONS FOR INS/GPS FUSION:")
        print(f"1. Apply time offset correction: GPS_time = GPS_time - {sync_results['time_offset']:.6f}")
        print(f"2. Use interpolation for GPS data to match IMU timestamps")
        print(f"3. Implement outlier detection for large time gaps")
        print(f"4. Monitor time synchronization drift during fusion")
        
        plt.show()

if __name__ == "__main__":
    main()
