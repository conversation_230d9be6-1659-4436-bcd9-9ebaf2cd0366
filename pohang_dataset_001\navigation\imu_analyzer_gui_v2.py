#!/usr/bin/env python3
"""
IMU/AHRS Data Interactive Analysis and Filtering Tool - Simplified Version

This script analyzes IMU data from Pohang dataset and provides
interactive filtering capabilities with GUI controls.

Based on Data Structure.md:
AHRS Format (11 columns):
| Unix time (s) | Orientation x | Orientation y | Orientation z | Orientation w | 
| Angular rate x (rad/s) | Angular rate y (rad/s) | Angular rate z (rad/s) | 
| Acceleration x (m/s²) | Acceleration y (m/s²) | Acceleration z (m/s²) |
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import <PERSON>lider, Button, CheckButtons, RadioButtons
import pandas as pd
from pathlib import Path
import logging
from scipy.signal import butter, filtfilt, medfilt
from scipy.ndimage import uniform_filter1d
from scipy.stats import zscore
import tkinter as tk
from tkinter import filedialog
from dataclasses import dataclass
import warnings

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class FilterConfig:
    """Simple filter configuration for each axis"""
    remove_outliers: bool = False
    outlier_threshold: float = 3.0
    apply_smoothing: bool = False
    smoothing_window: int = 5
    remove_spikes: bool = False
    spike_threshold: float = 2.0
    spike_kernel_size: int = 5

class IMUDataAnalyzer:
    def __init__(self, ahrs_file_path=None):
        """Initialize the IMU data analyzer"""
        self.ahrs_file_path = Path(ahrs_file_path) if ahrs_file_path else None
        self.original_data = None
        self.filtered_data = None
        self.sampling_rate = 100.0  # Default sampling rate
        
        # Component names and axes
        self.components = {
            'gyroscope': {
                'axes': ['X', 'Y', 'Z'],
                'fields': ['angular_rate_x', 'angular_rate_y', 'angular_rate_z'],
                'unit': 'rad/s',
                'title': 'Angular Rate'
            },
            'accelerometer': {
                'axes': ['X', 'Y', 'Z'],
                'fields': ['acceleration_x', 'acceleration_y', 'acceleration_z'],
                'unit': 'm/s²',
                'title': 'Acceleration'
            },
            'orientation': {
                'axes': ['X', 'Y', 'Z', 'W'],
                'fields': ['orientation_x', 'orientation_y', 'orientation_z', 'orientation_w'],
                'unit': 'quaternion',
                'title': 'Orientation'
            }
        }
        
        # Current selections
        self.current_tab = 'gyroscope'
        self.current_axis_index = 0  # 0=X, 1=Y, 2=Z, 3=W

        # Control update flag to prevent recursive updates
        self._updating_controls = False
        
        # Filter configurations for each axis of each component
        self.filter_configs = {}
        for comp_name, comp_info in self.components.items():
            self.filter_configs[comp_name] = {}
            for axis in comp_info['axes']:
                self.filter_configs[comp_name][axis] = FilterConfig()
        
        if self.ahrs_file_path and self.ahrs_file_path.exists():
            self.load_ahrs_data()
    
    def load_ahrs_data(self):
        """Load AHRS data from file"""
        try:
            logger.info(f"Loading AHRS data from: {self.ahrs_file_path}")
            
            # Load data (tab-delimited, no header)
            data = pd.read_csv(self.ahrs_file_path, sep='\t', header=None)
            
            # Parse columns according to Data Structure.md
            self.original_data = {
                'unix_time': data.iloc[:, 0].values,
                'orientation_x': data.iloc[:, 1].values,
                'orientation_y': data.iloc[:, 2].values,
                'orientation_z': data.iloc[:, 3].values,
                'orientation_w': data.iloc[:, 4].values,
                'angular_rate_x': data.iloc[:, 5].values,
                'angular_rate_y': data.iloc[:, 6].values,
                'angular_rate_z': data.iloc[:, 7].values,
                'acceleration_x': data.iloc[:, 8].values,
                'acceleration_y': data.iloc[:, 9].values,
                'acceleration_z': data.iloc[:, 10].values
            }
            
            # Convert to relative time (start from 0)
            self.original_data['rel_time'] = (
                self.original_data['unix_time'] - self.original_data['unix_time'][0]
            )
            
            # Calculate sampling rate
            if len(self.original_data['unix_time']) > 1:
                time_diffs = np.diff(self.original_data['unix_time'])
                self.sampling_rate = 1.0 / np.median(time_diffs)
                logger.info(f"Estimated sampling rate: {self.sampling_rate:.1f} Hz")
            
            logger.info(f"Loaded {len(self.original_data['unix_time'])} IMU data points")
            logger.info(f"Duration: {self.original_data['rel_time'][-1]:.1f} seconds")
            
            # Initialize filtered data as copy of original
            self.filtered_data = self.original_data.copy()
            
            # Print initial statistics
            self.print_statistics()
            
        except Exception as e:
            logger.error(f"Error loading AHRS data: {e}")
            raise
    
    def detect_outliers(self, data, threshold=3.0):
        """Detect outliers using Z-score method"""
        z_scores = np.abs(zscore(data))
        return z_scores > threshold
    
    def remove_spikes(self, data, kernel_size=5, threshold=2.0):
        """Remove spikes using adaptive median filter"""
        # Apply median filter
        median_filtered = medfilt(data, kernel_size=kernel_size)
        
        # Calculate residuals
        residuals = np.abs(data - median_filtered)
        spike_threshold = np.percentile(residuals, 95) * threshold
        
        # Replace spikes with median values
        spike_mask = residuals > spike_threshold
        filtered_data = data.copy()
        filtered_data[spike_mask] = median_filtered[spike_mask]
        
        return filtered_data
    
    def apply_smoothing(self, data, window_size=5):
        """Apply smoothing using moving average"""
        return uniform_filter1d(data, size=window_size, mode='nearest')
    
    def apply_filters(self):
        """Apply filters to all data based on configurations"""
        if self.original_data is None:
            return
        
        # Start with copy of original data
        self.filtered_data = self.original_data.copy()
        
        # Apply filters to each component and axis
        for comp_name, comp_info in self.components.items():
            for axis_idx, (axis, field) in enumerate(zip(comp_info['axes'], comp_info['fields'])):
                config = self.filter_configs[comp_name][axis]
                
                # Get original data for this axis
                orig_data = self.original_data[field].copy()
                filtered = orig_data.copy()
                
                # Apply filters in sequence
                if config.remove_outliers:
                    outlier_mask = self.detect_outliers(filtered, config.outlier_threshold)
                    # Interpolate outliers
                    good_indices = np.where(~outlier_mask)[0]
                    if len(good_indices) > 1:
                        filtered[outlier_mask] = np.interp(
                            np.where(outlier_mask)[0],
                            good_indices,
                            filtered[good_indices]
                        )
                    logger.info(f"{comp_name} {axis}: Removed {np.sum(outlier_mask)} outliers")
                
                if config.remove_spikes:
                    filtered = self.remove_spikes(
                        filtered, 
                        kernel_size=config.spike_kernel_size,
                        threshold=config.spike_threshold
                    )
                    logger.info(f"{comp_name} {axis}: Applied spike removal")
                
                if config.apply_smoothing:
                    filtered = self.apply_smoothing(filtered, config.smoothing_window)
                    logger.info(f"{comp_name} {axis}: Applied smoothing")
                
                # Update filtered data
                self.filtered_data[field] = filtered
        
        # Print updated statistics
        self.print_statistics()
    
    def print_statistics(self):
        """Print statistics to terminal"""
        print("\n" + "="*60)
        print("DATA STATISTICS")
        print("="*60)
        
        for comp_name, comp_info in self.components.items():
            print(f"\n{comp_name.upper()}:")
            print("-"*40)
            
            for axis, field in zip(comp_info['axes'], comp_info['fields']):
                orig = self.original_data[field]
                filt = self.filtered_data[field]
                
                print(f"\n  {axis}-axis ({comp_info['unit']}):")
                print(f"    Original  -> Filtered")
                print(f"    Mean: {np.mean(orig):8.4f} -> {np.mean(filt):8.4f}")
                print(f"    Std:  {np.std(orig):8.4f} -> {np.std(filt):8.4f}")
                print(f"    RMS:  {np.sqrt(np.mean(orig**2)):8.4f} -> {np.sqrt(np.mean(filt**2)):8.4f}")
    
    def create_interactive_gui(self):
        """Create interactive GUI with tabs for each sensor"""
        self.fig = plt.figure(figsize=(14, 8))
        self.fig.suptitle('IMU Data Interactive Filter', fontsize=14, fontweight='bold')
        
        # Create main layout
        # Left side: plots (70% width)
        # Right side: controls (30% width)
        gs = self.fig.add_gridspec(4, 2, width_ratios=[7, 3], hspace=0.3, wspace=0.2)

        # Create axes for plots (4 plots to accommodate orientation W-axis)
        self.plot_axes = []
        for i in range(4):
            ax = self.fig.add_subplot(gs[i, 0])
            self.plot_axes.append(ax)
        
        # Create control panel on the right
        control_gs = gs[:, 1].subgridspec(6, 1, hspace=0.5)
        
        # Tab selector (top)
        tab_ax = self.fig.add_subplot(control_gs[0])
        tab_ax.set_position([0.73, 0.85, 0.25, 0.12])
        self.tab_selector = RadioButtons(
            tab_ax,
            ['Gyroscope', 'Accelerometer', 'Orientation'],
            active=0
        )
        self.tab_selector.on_clicked(self.on_tab_changed)
        
        # Axis selector
        axis_ax = self.fig.add_subplot(control_gs[1])
        axis_ax.set_position([0.73, 0.68, 0.25, 0.15])
        self.axis_selector = RadioButtons(
            axis_ax,
            ['X-axis', 'Y-axis', 'Z-axis', 'W-axis'],
            active=0
        )
        self.axis_selector.on_clicked(self.on_axis_changed)
        
        # Filter checkboxes
        checkbox_ax = self.fig.add_subplot(control_gs[2])
        checkbox_ax.set_position([0.73, 0.45, 0.25, 0.2])
        self.checkboxes = CheckButtons(
            checkbox_ax,
            ['Remove Outliers', 'Remove Spikes', 'Apply Smoothing'],
            [False, False, False]
        )
        self.checkboxes.on_clicked(self.on_checkbox_changed)
        
        # Sliders
        # Outlier threshold
        outlier_ax = self.fig.add_subplot(control_gs[3])
        outlier_ax.set_position([0.73, 0.35, 0.22, 0.03])
        self.outlier_slider = Slider(outlier_ax, 'Outlier σ', 0.1, 10, valinit=3.0)
        
        # Spike threshold
        spike_ax = self.fig.add_subplot(control_gs[4])
        spike_ax.set_position([0.73, 0.30, 0.22, 0.03])
        self.spike_slider = Slider(spike_ax, 'Spike Thr', 0.1, 10, valinit=2.0)
        
        # Smoothing window
        smooth_ax = self.fig.add_subplot(control_gs[5])
        smooth_ax.set_position([0.73, 0.25, 0.22, 0.03])
        self.smooth_slider = Slider(smooth_ax, 'Smooth Win', 1, 50, valinit=5, valfmt='%d')
        
        # Buttons
        # Load button
        load_ax = plt.axes([0.73, 0.15, 0.11, 0.04])
        self.load_button = Button(load_ax, 'Load File')
        self.load_button.on_clicked(self.on_load_file)
        
        # Update button
        update_ax = plt.axes([0.85, 0.15, 0.11, 0.04])
        self.update_button = Button(update_ax, 'Update')
        self.update_button.on_clicked(self.on_update_filters)
        
        # Reset button
        reset_ax = plt.axes([0.73, 0.10, 0.11, 0.04])
        self.reset_button = Button(reset_ax, 'Reset')
        self.reset_button.on_clicked(self.on_reset_filters)
        
        # Export button
        export_ax = plt.axes([0.85, 0.10, 0.11, 0.04])
        self.export_button = Button(export_ax, 'Export')
        self.export_button.on_clicked(self.on_export_data)
        
        # Connect slider callbacks
        self.outlier_slider.on_changed(self.on_slider_changed)
        self.spike_slider.on_changed(self.on_slider_changed)
        self.smooth_slider.on_changed(self.on_slider_changed)
        
        # Initial plot
        self.update_plots()
        self.update_controls()
        
        plt.show()
        return self.fig
    
    def update_plots(self):
        """Update plots for current tab"""
        if self.original_data is None:
            return

        # Apply filters
        self.apply_filters()

        # Get current component info
        comp_info = self.components[self.current_tab]
        num_axes = len(comp_info['axes'])

        # Clear all plot axes first
        for ax in self.plot_axes:
            ax.clear()
            ax.set_visible(False)  # Hide all initially

        # Update plots for available axes
        for idx in range(num_axes):
            ax = self.plot_axes[idx]
            axis = comp_info['axes'][idx]
            field = comp_info['fields'][idx]

            ax.set_visible(True)  # Show this plot

            # Get data
            time = self.original_data['rel_time']
            orig_data = self.original_data[field]
            filt_data = self.filtered_data[field]

            # Plot
            ax.plot(time, orig_data, 'b-', alpha=0.5, linewidth=0.8, label='Original')
            ax.plot(time, filt_data, 'r-', linewidth=1.2, label='Filtered')

            # Highlight current selected axis
            if idx == self.current_axis_index:
                ax.set_facecolor('#f0f0f0')
            else:
                ax.set_facecolor('white')

            # Labels
            ax.set_ylabel(f'{axis}-axis ({comp_info["unit"]})')
            ax.grid(True, alpha=0.3)
            ax.legend(loc='upper right')

            # Set xlabel for the last visible plot
            if idx == num_axes - 1:
                ax.set_xlabel('Time (s)')

            # Title for top plot
            if idx == 0:
                ax.set_title(f'{comp_info["title"]} - Time Series')

        plt.draw()
    
    def update_controls(self):
        """Update controls to match current selection"""
        # Get current config
        axis = self.components[self.current_tab]['axes'][self.current_axis_index]
        config = self.filter_configs[self.current_tab][axis]

        # Use a flag to prevent recursive updates
        self._updating_controls = True

        # Update checkboxes
        states = [
            config.remove_outliers,
            config.remove_spikes,
            config.apply_smoothing
        ]
        for i, state in enumerate(states):
            if self.checkboxes.get_status()[i] != state:
                self.checkboxes.set_active(i)

        # Update sliders (these don't trigger events when set programmatically)
        self.outlier_slider.set_val(config.outlier_threshold)
        self.spike_slider.set_val(config.spike_threshold)
        self.smooth_slider.set_val(config.smoothing_window)

        # Clear the flag
        self._updating_controls = False
    
    def on_tab_changed(self, label):
        """Handle tab selection change"""
        self.current_tab = label.lower()
        self.current_axis_index = 0  # Reset to X-axis
        self.update_controls()
        self.update_plots()
    
    def on_axis_changed(self, label):
        """Handle axis selection change"""
        axis_map = {'X-axis': 0, 'Y-axis': 1, 'Z-axis': 2, 'W-axis': 3}
        if label in axis_map:
            new_index = axis_map[label]
            # Check if the selected axis is valid for current component
            max_axes = len(self.components[self.current_tab]['axes'])
            if new_index < max_axes:
                self.current_axis_index = new_index
                self.update_controls()
                self.update_plots()
    
    def on_checkbox_changed(self, label):
        """Handle checkbox changes"""
        # Skip if we're updating controls programmatically
        if getattr(self, '_updating_controls', False):
            return

        # Get current config
        axis = self.components[self.current_tab]['axes'][self.current_axis_index]
        config = self.filter_configs[self.current_tab][axis]

        checkbox_map = {
            'Remove Outliers': 'remove_outliers',
            'Remove Spikes': 'remove_spikes',
            'Apply Smoothing': 'apply_smoothing'
        }

        if label in checkbox_map:
            param_name = checkbox_map[label]
            checkbox_index = list(checkbox_map.keys()).index(label)
            checkbox_state = self.checkboxes.get_status()[checkbox_index]
            setattr(config, param_name, checkbox_state)

            # Auto-update plots
            self.update_plots()
    
    def on_slider_changed(self, val):
        """Handle slider value changes"""
        # Get current config
        axis = self.components[self.current_tab]['axes'][self.current_axis_index]
        config = self.filter_configs[self.current_tab][axis]

        config.outlier_threshold = self.outlier_slider.val
        config.spike_threshold = self.spike_slider.val
        config.smoothing_window = int(self.smooth_slider.val)

        # Note: Plots are updated when user clicks "Update" button or changes checkboxes
    
    def on_update_filters(self, event):
        """Update filters when button is clicked"""
        self.update_plots()
    
    def on_reset_filters(self, event):
        """Reset all filters for current axis"""
        axis = self.components[self.current_tab]['axes'][self.current_axis_index]
        config = self.filter_configs[self.current_tab][axis]
        
        config.remove_outliers = False
        config.outlier_threshold = 3.0
        config.remove_spikes = False
        config.spike_threshold = 2.0
        config.spike_kernel_size = 5
        config.apply_smoothing = False
        config.smoothing_window = 5
        
        self.update_controls()
        self.update_plots()
    
    def on_load_file(self, event):
        """Load new AHRS file using file dialog"""
        try:
            root = tk.Tk()
            root.withdraw()
            
            new_file_path = filedialog.askopenfilename(
                title="Select AHRS data file (ahrs.txt)",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                initialdir=str(self.ahrs_file_path.parent) if self.ahrs_file_path else "."
            )
            
            root.destroy()
            
            if new_file_path:
                self.ahrs_file_path = Path(new_file_path)
                logger.info(f"Loading new AHRS file: {self.ahrs_file_path}")
                self.load_ahrs_data()
                self.update_plots()
                
        except Exception as e:
            logger.error(f"Error loading AHRS file: {e}")
    
    def on_export_data(self, event):
        """Export filtered data to new file"""
        try:
            root = tk.Tk()
            root.withdraw()
            
            input_path = Path(self.ahrs_file_path)
            default_filename = f"{input_path.stem}_filtered{input_path.suffix}"
            
            output_path = filedialog.asksaveasfilename(
                title="Save filtered AHRS data",
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                initialdir=str(input_path.parent),
                initialfile=default_filename
            )
            
            root.destroy()
            
            if output_path:
                output_path = Path(output_path)
                
                # Prepare data for export
                export_data = []
                
                for i in range(len(self.filtered_data['unix_time'])):
                    row = [
                        self.filtered_data['unix_time'][i],
                        self.filtered_data['orientation_x'][i],
                        self.filtered_data['orientation_y'][i],
                        self.filtered_data['orientation_z'][i],
                        self.filtered_data['orientation_w'][i],
                        self.filtered_data['angular_rate_x'][i],
                        self.filtered_data['angular_rate_y'][i],
                        self.filtered_data['angular_rate_z'][i],
                        self.filtered_data['acceleration_x'][i],
                        self.filtered_data['acceleration_y'][i],
                        self.filtered_data['acceleration_z'][i]
                    ]
                    export_data.append(row)
                
                # Save to file
                df_export = pd.DataFrame(export_data)
                df_export.to_csv(output_path, sep='\t', header=False, index=False, float_format='%.9f')
                
                logger.info(f"Filtered AHRS data exported to: {output_path}")
                print(f"\n✓ Exported filtered data to: {output_path}")
                
        except Exception as e:
            logger.error(f"Error exporting data: {e}")
            print(f"\n✗ Export failed: {str(e)}")


def main():
    """Main function to run the IMU data analyzer"""
    
    print("🎛️ IMU/AHRS Data Interactive Analysis and Filtering Tool")
    print("=" * 60)
    print("Simplified Version - Individual Axis Filtering")
    print("=" * 60)
    
    # Use tkinter file dialog to select file
    root = tk.Tk()
    root.withdraw()
    
    ahrs_file_path = filedialog.askopenfilename(
        title="Select AHRS data file (ahrs.txt)",
        filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
        initialdir=str(Path.cwd())
    )
    
    root.destroy()
    
    if not ahrs_file_path:
        print("No file selected. Exiting...")
        return
    
    try:
        # Create analyzer
        print("\n🎛️ Starting interactive filtering GUI...")
        analyzer = IMUDataAnalyzer(ahrs_file_path)
        
        # Show interactive GUI
        analyzer.create_interactive_gui()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
