import numpy as np
import matplotlib.pyplot as plt

def analyze_gps_accuracy_calculation():
    """
    Analyze how GPS accuracy is calculated based on the dataset documentation.
    
    According to the dataset documentation:
    GPS Format: 11 columns in tab-delimited format:
    | Unix time (s) | GPS time (s) | Latitude (deg) | Hemisphere of latitude (N/S) | 
    | Longitude (deg) | Hemisphere of longitude (E/W) | Heading (deg) | GPS quality indicator | 
    | Number of satellites | Horizontal dilution of precision | Geoid height (m) |
    
    Column 10 (index 9): Horizontal Dilution of Precision (HDOP)
    Column 11 (index 10): Geoid height (m) - This is what we've been calling "accuracy"
    """
    
    print("=== GPS Accuracy Calculation Analysis ===")
    print("Based on Pohang Dataset Documentation")
    print()
    
    # Load and parse GPS data
    gps_times = []
    latitudes = []
    longitudes = []
    headings = []
    quality_indicators = []
    num_satellites = []
    hdop_values = []
    geoid_heights = []
    
    with open('gps.txt', 'r') as f:
        for line_num, line in enumerate(f, 1):
            parts = line.strip().split('\t')
            if len(parts) >= 11:
                try:
                    unix_time = float(parts[0])
                    gps_time = float(parts[1])
                    lat = float(parts[2])
                    lat_hem = parts[3]
                    lon = float(parts[4])
                    lon_hem = parts[5]
                    heading = float(parts[6])
                    quality = int(parts[7])
                    num_sats = int(parts[8])
                    hdop = float(parts[9])
                    geoid_height = float(parts[10])
                    
                    gps_times.append(unix_time)
                    latitudes.append(lat)
                    longitudes.append(lon)
                    headings.append(heading)
                    quality_indicators.append(quality)
                    num_satellites.append(num_sats)
                    hdop_values.append(hdop)
                    geoid_heights.append(geoid_height)
                    
                except ValueError as e:
                    print(f"Error parsing line {line_num}: {e}")
                    continue
    
    # Convert to numpy arrays
    gps_times = np.array(gps_times)
    hdop_values = np.array(hdop_values)
    geoid_heights = np.array(geoid_heights)
    quality_indicators = np.array(quality_indicators)
    num_satellites = np.array(num_satellites)
    
    print(f"Loaded {len(gps_times)} GPS measurements")
    print()
    
    # Analyze what we've been calling "accuracy"
    print("=== Analysis of Column 11 (Geoid Height) ===")
    print("This is what we've been interpreting as 'GPS accuracy'")
    print(f"Mean geoid height: {np.mean(geoid_heights):.3f} m")
    print(f"Std geoid height: {np.std(geoid_heights):.3f} m")
    print(f"Min geoid height: {np.min(geoid_heights):.3f} m")
    print(f"Max geoid height: {np.max(geoid_heights):.3f} m")
    print()
    print("NOTE: Geoid height is NOT GPS accuracy!")
    print("Geoid height is the height of the geoid above the WGS84 ellipsoid.")
    print("For South Korea, typical geoid height is around 6-7 meters.")
    print()
    
    # Analyze HDOP (Horizontal Dilution of Precision)
    print("=== Analysis of Column 10 (HDOP) ===")
    print("HDOP = Horizontal Dilution of Precision")
    print("This is the actual GPS accuracy indicator!")
    print(f"Mean HDOP: {np.mean(hdop_values):.3f}")
    print(f"Std HDOP: {np.std(hdop_values):.3f}")
    print(f"Min HDOP: {np.min(hdop_values):.3f}")
    print(f"Max HDOP: {np.max(hdop_values):.3f}")
    print()
    
    # Calculate actual GPS accuracy from HDOP
    print("=== GPS Accuracy Calculation from HDOP ===")
    print("GPS horizontal accuracy ≈ HDOP × User Equivalent Range Error (UERE)")
    print("For standard GPS: UERE ≈ 3-5 meters")
    print("For this dataset (GPS without RTK): UERE ≈ 4 meters")
    print()
    
    uere = 4.0  # meters, typical for standard GPS
    calculated_accuracy = hdop_values * uere
    
    print(f"Calculated GPS accuracy (HDOP × {uere}m):")
    print(f"Mean accuracy: {np.mean(calculated_accuracy):.3f} m")
    print(f"Std accuracy: {np.std(calculated_accuracy):.3f} m")
    print(f"Min accuracy: {np.min(calculated_accuracy):.3f} m")
    print(f"Max accuracy: {np.max(calculated_accuracy):.3f} m")
    print()
    
    # Analyze GPS quality and satellite count
    print("=== GPS Quality Analysis ===")
    unique_quality = np.unique(quality_indicators)
    print(f"GPS Quality indicators present: {unique_quality}")
    print("Quality meanings:")
    print("  0 = Invalid")
    print("  1 = GPS fix (SPS)")
    print("  2 = DGPS fix")
    print("  3 = PPS fix")
    print("  4 = Real Time Kinematic")
    print("  5 = Float RTK")
    print("  6 = estimated (dead reckoning)")
    print("  7 = Manual input mode")
    print("  8 = Simulation mode")
    print()
    
    for quality in unique_quality:
        count = np.sum(quality_indicators == quality)
        percentage = (count / len(quality_indicators)) * 100
        print(f"Quality {quality}: {count} measurements ({percentage:.1f}%)")
    print()
    
    print(f"Number of satellites:")
    print(f"Mean: {np.mean(num_satellites):.1f}")
    print(f"Min: {np.min(num_satellites)}")
    print(f"Max: {np.max(num_satellites)}")
    print()
    
    # Create comprehensive plot
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # Plot 1: HDOP over time
    time_relative = (gps_times - gps_times[0]) / 60  # Convert to minutes
    axes[0, 0].plot(time_relative, hdop_values, 'b-', alpha=0.7)
    axes[0, 0].set_xlabel('Time (minutes)')
    axes[0, 0].set_ylabel('HDOP')
    axes[0, 0].set_title('Horizontal Dilution of Precision (HDOP) Over Time')
    axes[0, 0].grid(True, alpha=0.3)
    
    # Plot 2: Calculated accuracy over time
    axes[0, 1].plot(time_relative, calculated_accuracy, 'r-', alpha=0.7)
    axes[0, 1].set_xlabel('Time (minutes)')
    axes[0, 1].set_ylabel('GPS Accuracy (m)')
    axes[0, 1].set_title('Calculated GPS Accuracy (HDOP × UERE) Over Time')
    axes[0, 1].grid(True, alpha=0.3)
    
    # Plot 3: Number of satellites over time
    axes[0, 2].plot(time_relative, num_satellites, 'g-', alpha=0.7)
    axes[0, 2].set_xlabel('Time (minutes)')
    axes[0, 2].set_ylabel('Number of Satellites')
    axes[0, 2].set_title('Number of Satellites Over Time')
    axes[0, 2].grid(True, alpha=0.3)
    
    # Plot 4: HDOP distribution
    axes[1, 0].hist(hdop_values, bins=50, alpha=0.7, color='blue', edgecolor='black')
    axes[1, 0].axvline(np.mean(hdop_values), color='red', linestyle='--', 
                       label=f'Mean: {np.mean(hdop_values):.2f}')
    axes[1, 0].set_xlabel('HDOP')
    axes[1, 0].set_ylabel('Frequency')
    axes[1, 0].set_title('HDOP Distribution')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # Plot 5: Calculated accuracy distribution
    axes[1, 1].hist(calculated_accuracy, bins=50, alpha=0.7, color='red', edgecolor='black')
    axes[1, 1].axvline(np.mean(calculated_accuracy), color='blue', linestyle='--', 
                       label=f'Mean: {np.mean(calculated_accuracy):.2f}m')
    axes[1, 1].set_xlabel('GPS Accuracy (m)')
    axes[1, 1].set_ylabel('Frequency')
    axes[1, 1].set_title('Calculated GPS Accuracy Distribution')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    # Plot 6: Geoid height (what we mistakenly called accuracy)
    axes[1, 2].hist(geoid_heights, bins=50, alpha=0.7, color='orange', edgecolor='black')
    axes[1, 2].axvline(np.mean(geoid_heights), color='blue', linestyle='--', 
                       label=f'Mean: {np.mean(geoid_heights):.2f}m')
    axes[1, 2].set_xlabel('Geoid Height (m)')
    axes[1, 2].set_ylabel('Frequency')
    axes[1, 2].set_title('Geoid Height Distribution\n(NOT GPS Accuracy)')
    axes[1, 2].legend()
    axes[1, 2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('gps_accuracy_calculation_explained.png', dpi=300, bbox_inches='tight')
    print("Plot saved as 'gps_accuracy_calculation_explained.png'")
    print()
    
    # Final explanation
    print("=== CONCLUSION ===")
    print("❌ MISTAKE: We were using Geoid Height as GPS accuracy")
    print(f"   Geoid height: {np.mean(geoid_heights):.1f}m (constant ~6-7m for South Korea)")
    print()
    print("✅ CORRECT: GPS accuracy should be calculated from HDOP")
    print(f"   HDOP: {np.mean(hdop_values):.3f}")
    print(f"   Calculated GPS accuracy: {np.mean(calculated_accuracy):.1f}m")
    print()
    print("The actual GPS accuracy is much better than we thought!")
    print(f"Real GPS accuracy: ~{np.mean(calculated_accuracy):.1f}m")
    print(f"Measured position errors: ~5.1m")
    print()
    print("This suggests there might be other sources of error:")
    print("- Coordinate transformation errors")
    print("- Multipath effects")
    print("- Atmospheric delays")
    print("- Baseline measurement uncertainties")
    
    return calculated_accuracy

if __name__ == "__main__":
    calculated_accuracy = analyze_gps_accuracy_calculation()
