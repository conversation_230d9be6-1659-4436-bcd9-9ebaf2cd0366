import numpy as np
import matplotlib.pyplot as plt

def create_cross_correlation_explanation():
    """Create a visual explanation of cross-correlation for time synchronization."""
    
    # Simulate your actual results
    offset_range = np.linspace(-10, 10, 200)
    
    # Create a correlation curve similar to your results
    # Peak at +0.352 seconds with score of 0.24
    best_offset = 0.352
    best_score = 0.24
    
    # Create a realistic correlation curve
    correlation_scores = []
    for offset in offset_range:
        # Gaussian-like peak around the best offset
        score = best_score * np.exp(-0.5 * ((offset - best_offset) / 2.0)**2)
        # Add some noise and secondary peaks
        score += 0.05 * np.exp(-0.5 * ((offset + 3.0) / 1.5)**2)  # Secondary peak
        score += 0.03 * np.random.random()  # Noise
        score = max(0, min(1, score))  # Clamp between 0 and 1
        correlation_scores.append(score)
    
    correlation_scores = np.array(correlation_scores)
    
    # Create the explanation plot
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # Plot 1: Cross-correlation curve with detailed explanation
    axes[0, 0].plot(offset_range, correlation_scores, 'purple', linewidth=3, label='Correlation Score')
    axes[0, 0].axvline(best_offset, color='red', linestyle='--', linewidth=2, 
                      label=f'Best Offset: +{best_offset:.3f}s')
    axes[0, 0].axhline(best_score, color='orange', linestyle=':', alpha=0.7,
                      label=f'Best Score: {best_score:.3f}')
    
    # Add annotations
    axes[0, 0].annotate(f'PEAK: {best_score:.1%} events match\nwhen IMU shifted by +{best_offset:.3f}s', 
                       xy=(best_offset, best_score), xytext=(best_offset + 3, best_score + 0.1),
                       arrowprops=dict(arrowstyle='->', color='red', lw=2),
                       fontsize=12, bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
    
    axes[0, 0].set_xlabel('Time Offset Applied to IMU (seconds)', fontsize=12)
    axes[0, 0].set_ylabel('Correlation Score\n(Fraction of GPS events matched)', fontsize=12)
    axes[0, 0].set_title('Cross-Correlation: Finding the Best Time Offset', fontsize=14, fontweight='bold')
    axes[0, 0].legend(fontsize=11)
    axes[0, 0].grid(True, alpha=0.3)
    
    # Add interpretation zones
    axes[0, 0].axhspan(0.7, 1.0, alpha=0.1, color='green', label='Excellent')
    axes[0, 0].axhspan(0.5, 0.7, alpha=0.1, color='yellow', label='Good')
    axes[0, 0].axhspan(0.3, 0.5, alpha=0.1, color='orange', label='Fair')
    axes[0, 0].axhspan(0.0, 0.3, alpha=0.1, color='red', label='Poor')
    
    # Plot 2: Timeline explanation - BEFORE correction
    time_base = np.array([10, 25, 40, 60, 75])  # GPS event times
    imu_events_wrong = time_base - best_offset + np.random.normal(0, 0.1, len(time_base))  # IMU events (wrong timing)
    
    axes[0, 1].scatter(time_base, np.ones(len(time_base)), color='blue', s=100, marker='s', 
                      label='GPS Turn Events', alpha=0.8)
    axes[0, 1].scatter(imu_events_wrong, np.ones(len(imu_events_wrong)) * 1.2, color='red', s=100, marker='^', 
                      label='IMU Rotation Events', alpha=0.8)
    
    # Draw misalignment lines
    for i in range(len(time_base)):
        axes[0, 1].plot([time_base[i], imu_events_wrong[i]], [1, 1.2], 'k--', alpha=0.5, linewidth=1)
    
    axes[0, 1].set_ylim(0.8, 1.4)
    axes[0, 1].set_xlabel('Time (seconds)', fontsize=12)
    axes[0, 1].set_title('BEFORE Correction: Events Misaligned', fontsize=14, fontweight='bold', color='red')
    axes[0, 1].legend(fontsize=11)
    axes[0, 1].grid(True, alpha=0.3)
    axes[0, 1].text(0.5, 0.95, f'IMU events happen {best_offset:.3f}s too early!', 
                   transform=axes[0, 1].transAxes, fontsize=11, 
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="red", alpha=0.3))
    
    # Plot 3: Timeline explanation - AFTER correction
    imu_events_corrected = imu_events_wrong + best_offset  # Apply correction
    
    axes[1, 0].scatter(time_base, np.ones(len(time_base)), color='blue', s=100, marker='s', 
                      label='GPS Turn Events', alpha=0.8)
    axes[1, 0].scatter(imu_events_corrected, np.ones(len(imu_events_corrected)) * 1.2, color='green', s=100, marker='^', 
                      label='IMU Rotation Events (Corrected)', alpha=0.8)
    
    # Draw alignment lines
    for i in range(len(time_base)):
        axes[1, 0].plot([time_base[i], imu_events_corrected[i]], [1, 1.2], 'g-', alpha=0.7, linewidth=2)
    
    axes[1, 0].set_ylim(0.8, 1.4)
    axes[1, 0].set_xlabel('Time (seconds)', fontsize=12)
    axes[1, 0].set_title('AFTER Correction: Events Aligned', fontsize=14, fontweight='bold', color='green')
    axes[1, 0].legend(fontsize=11)
    axes[1, 0].grid(True, alpha=0.3)
    axes[1, 0].text(0.5, 0.95, f'IMU_corrected = IMU_original + {best_offset:.3f}s', 
                   transform=axes[1, 0].transAxes, fontsize=11, 
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="green", alpha=0.3))
    
    # Plot 4: Interpretation guide
    axes[1, 1].axis('off')
    
    explanation_text = f"""
🔍 CROSS-CORRELATION INTERPRETATION

📊 Your Results:
• Best Offset: +{best_offset:.3f} seconds
• Correlation Score: {best_score:.1%}
• Quality: Poor (24% match rate)

🎯 What This Means:
• IMU events happen {best_offset:.3f}s BEFORE GPS events
• Only {best_score:.0%} of GPS turns have matching IMU rotations
• {100-best_score*100:.0f}% of events don't correlate

⚙️ Why Low Correlation?
• Different sensor sensitivities
• GPS detects large-scale maneuvers
• IMU detects all rotations (including small ones)
• Some GPS turns might be too gradual for IMU peaks

✅ Correction Formula:
IMU_time_corrected = IMU_time + {best_offset:.3f}

🎯 Expected Improvement:
• Better event alignment
• More accurate INS/GPS fusion
• Reduced position errors in navigation
"""
    
    axes[1, 1].text(0.05, 0.95, explanation_text, transform=axes[1, 1].transAxes, 
                   fontsize=11, verticalalignment='top', fontfamily='monospace',
                   bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('cross_correlation_explanation.png', dpi=300, bbox_inches='tight')
    print("✅ Cross-correlation explanation saved as 'cross_correlation_explanation.png'")
    
    return fig

def main():
    """Create the explanation visualization."""
    print("=== CROSS-CORRELATION EXPLANATION ===")
    print("Creating visual explanation of your time synchronization results...\n")
    
    fig = create_cross_correlation_explanation()
    
    print("\n📚 KEY CONCEPTS:")
    print("1. CROSS-CORRELATION: Tests different time shifts to find best alignment")
    print("2. BEST OFFSET: The time shift that maximizes event matching")
    print("3. CORRELATION SCORE: Percentage of GPS events that have IMU matches")
    print("4. TIME CORRECTION: Add the offset to IMU timestamps for synchronization")
    
    print("\n🔧 YOUR SPECIFIC CASE:")
    print("• IMU clock runs 0.352 seconds AHEAD of GPS clock")
    print("• To fix: Add 0.352s to all IMU timestamps")
    print("• This will align turn events between GPS and IMU")
    print("• Low correlation (24%) suggests different event sensitivities")
    
    plt.show()

if __name__ == "__main__":
    main()
