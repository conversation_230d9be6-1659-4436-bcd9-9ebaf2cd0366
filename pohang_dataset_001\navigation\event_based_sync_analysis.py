import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from scipy.signal import find_peaks

def load_imu_data(filename):
    """Load IMU (AHRS) data."""
    data = []
    with open(filename, 'r') as f:
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 11:
                unix_time = float(parts[0])
                # Angular rates: wx, wy, wz (rad/s) - columns 5,6,7
                wx, wy, wz = float(parts[5]), float(parts[6]), float(parts[7])
                # Accelerations: ax, ay, az (m/s²) - columns 8,9,10
                ax, ay, az = float(parts[8]), float(parts[9]), float(parts[10])
                
                data.append([unix_time, wx, wy, wz, ax, ay, az])
    
    return np.array(data)

def load_gps_data(filename):
    """Load GPS data."""
    data = []
    with open(filename, 'r') as f:
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 11:
                unix_time = float(parts[0])
                lat = float(parts[2])
                lat_hem = parts[3]
                lon = float(parts[4])
                lon_hem = parts[5]
                heading = float(parts[6])
                
                # Apply hemisphere signs
                if lat_hem == 'S':
                    lat = -lat
                if lon_hem == 'W':
                    lon = -lon
                
                data.append([unix_time, lat, lon, heading])
    
    return np.array(data)

def detect_gps_events(gps_data, min_prominence=2.0):
    """Detect significant events in GPS data: turns, direction changes."""
    times = gps_data[:, 0]
    headings = gps_data[:, 3]
    
    # Calculate heading rate (angular velocity from GPS)
    dt = np.diff(times)
    dt[dt == 0] = 1e-6  # Avoid division by zero
    
    # Handle heading wraparound (0-360 degrees)
    heading_diff = np.diff(headings)
    heading_diff[heading_diff > 180] -= 360
    heading_diff[heading_diff < -180] += 360
    
    heading_rate = heading_diff / dt  # degrees/second
    
    # Smooth the heading rate to reduce noise
    if len(heading_rate) > 10:
        heading_rate_smooth = signal.savgol_filter(heading_rate, 
                                                  window_length=min(11, len(heading_rate)//2*2+1), 
                                                  polyorder=2)
    else:
        heading_rate_smooth = heading_rate
    
    # Find peaks in absolute heading rate (significant turns)
    abs_heading_rate = np.abs(heading_rate_smooth)
    peaks, properties = find_peaks(abs_heading_rate, 
                                  prominence=min_prominence,
                                  distance=10)  # At least 10 samples apart
    
    # Get event times and magnitudes
    event_times = times[1:][peaks]  # times[1:] because heading_rate is diff
    event_magnitudes = abs_heading_rate[peaks]
    
    print(f"GPS Events Detected: {len(event_times)} significant turns")
    print(f"Turn rates: {np.mean(event_magnitudes):.1f} ± {np.std(event_magnitudes):.1f} deg/s")
    
    return {
        'times': event_times,
        'magnitudes': event_magnitudes,
        'heading_rate': heading_rate_smooth,
        'heading_rate_times': times[1:]
    }

def detect_imu_events(imu_data, min_prominence=0.05):
    """Detect significant events in IMU data: angular rate peaks."""
    times = imu_data[:, 0]
    wx, wy, wz = imu_data[:, 1], imu_data[:, 2], imu_data[:, 3]
    
    # Calculate total angular rate magnitude
    angular_rate_mag = np.sqrt(wx**2 + wy**2 + wz**2)
    
    # Smooth the angular rate to reduce noise
    if len(angular_rate_mag) > 10:
        angular_rate_smooth = signal.savgol_filter(angular_rate_mag, 
                                                  window_length=min(11, len(angular_rate_mag)//2*2+1), 
                                                  polyorder=2)
    else:
        angular_rate_smooth = angular_rate_mag
    
    # Find peaks in angular rate (significant rotations)
    peaks, properties = find_peaks(angular_rate_smooth, 
                                  prominence=min_prominence,
                                  distance=50)  # At least 50 samples apart (IMU is high rate)
    
    # Get event times and magnitudes
    event_times = times[peaks]
    event_magnitudes = angular_rate_smooth[peaks]
    
    print(f"IMU Events Detected: {len(event_times)} significant rotations")
    print(f"Angular rates: {np.mean(event_magnitudes):.3f} ± {np.std(event_magnitudes):.3f} rad/s")
    
    return {
        'times': event_times,
        'magnitudes': event_magnitudes,
        'angular_rate': angular_rate_smooth,
        'angular_rate_times': times
    }

def cross_correlate_events(gps_events, imu_events, max_offset=10.0):
    """Cross-correlate GPS and IMU events to find time synchronization offset."""
    
    gps_times = gps_events['times']
    imu_times = imu_events['times']
    
    if len(gps_times) == 0 or len(imu_times) == 0:
        print("❌ No events detected in one or both datasets!")
        return None
    
    print(f"\nCross-correlating {len(gps_times)} GPS events with {len(imu_times)} IMU events...")
    
    # Create time offset range to test
    offset_range = np.linspace(-max_offset, max_offset, 200)
    correlation_scores = []
    
    for offset in offset_range:
        # Apply offset to IMU times
        imu_times_shifted = imu_times + offset
        
        # Count how many GPS events have a corresponding IMU event within tolerance
        matches = 0
        tolerance = 0.5  # seconds
        
        for gps_time in gps_times:
            # Find closest IMU event
            time_diffs = np.abs(imu_times_shifted - gps_time)
            min_diff = np.min(time_diffs)
            
            if min_diff <= tolerance:
                matches += 1
        
        # Correlation score: percentage of GPS events that have IMU matches
        score = matches / len(gps_times)
        correlation_scores.append(score)
    
    correlation_scores = np.array(correlation_scores)
    
    # Find best offset
    best_idx = np.argmax(correlation_scores)
    best_offset = offset_range[best_idx]
    best_score = correlation_scores[best_idx]
    
    print(f"\n=== CROSS-CORRELATION RESULTS ===")
    print(f"Best time offset: {best_offset:.3f} seconds")
    print(f"Correlation score: {best_score:.3f} ({best_score*100:.1f}% of GPS events matched)")

    # Analyze quality of synchronization
    if best_score > 0.7:
        sync_quality = "Excellent"
    elif best_score > 0.5:
        sync_quality = "Good"
    elif best_score > 0.3:
        sync_quality = "Fair"
    else:
        sync_quality = "Poor"

    print(f"Synchronization quality: {sync_quality}")

    # Explain the cross-correlation meaning
    print(f"\n=== CROSS-CORRELATION INTERPRETATION ===")
    print(f"📊 What the best offset means:")
    print(f"   • IMU events happen {best_offset:.3f} seconds BEFORE corresponding GPS events")
    print(f"   • To synchronize: Add {best_offset:.3f}s to IMU timestamps")
    print(f"   • This makes IMU and GPS events align in time")
    print(f"")
    print(f"📈 Correlation score meaning:")
    print(f"   • Out of {len(gps_times)} GPS turn events detected")
    print(f"   • {int(best_score * len(gps_times))} events ({best_score*100:.1f}%) have matching IMU rotations")
    print(f"   • {len(gps_times) - int(best_score * len(gps_times))} events ({(1-best_score)*100:.1f}%) don't match")
    print(f"")
    if best_score < 0.5:
        print(f"⚠️  Low correlation suggests:")
        print(f"   • Different sensitivity in event detection")
        print(f"   • GPS and IMU might detect different types of movements")
        print(f"   • Some events might be too small to detect in both sensors")
        print(f"   • Time synchronization might have additional complexities")
    
    return {
        'best_offset': best_offset,
        'best_score': best_score,
        'offset_range': offset_range,
        'correlation_scores': correlation_scores,
        'sync_quality': sync_quality
    }

def plot_event_analysis(gps_events, imu_events, correlation_result):
    """Plot the event-based synchronization analysis."""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # Plot 1: GPS heading rate and detected events
    axes[0, 0].plot(gps_events['heading_rate_times'], gps_events['heading_rate'], 'b-', alpha=0.7, label='Heading Rate')
    axes[0, 0].scatter(gps_events['times'], gps_events['magnitudes'], color='red', s=50, zorder=5, label='Detected Events')
    axes[0, 0].set_xlabel('Time (Unix)')
    axes[0, 0].set_ylabel('Heading Rate (deg/s)')
    axes[0, 0].set_title('GPS: Heading Rate and Detected Turn Events')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # Plot 2: IMU angular rate and detected events
    axes[0, 1].plot(imu_events['angular_rate_times'], imu_events['angular_rate'], 'g-', alpha=0.7, label='Angular Rate Magnitude')
    axes[0, 1].scatter(imu_events['times'], imu_events['magnitudes'], color='red', s=50, zorder=5, label='Detected Events')
    axes[0, 1].set_xlabel('Time (Unix)')
    axes[0, 1].set_ylabel('Angular Rate (rad/s)')
    axes[0, 1].set_title('IMU: Angular Rate and Detected Rotation Events')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # Plot 3: Cross-correlation results
    if correlation_result:
        axes[1, 0].plot(correlation_result['offset_range'], correlation_result['correlation_scores'], 'purple', linewidth=2)
        axes[1, 0].axvline(correlation_result['best_offset'], color='red', linestyle='--', 
                          label=f'Best offset: {correlation_result["best_offset"]:.3f}s')
        axes[1, 0].set_xlabel('Time Offset (seconds)')
        axes[1, 0].set_ylabel('Correlation Score')
        axes[1, 0].set_title('Cross-Correlation: GPS vs IMU Events')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
    
    # Plot 4: Event timing comparison
    if len(gps_events['times']) > 0 and len(imu_events['times']) > 0:
        # Normalize times to start from 0
        gps_times_norm = gps_events['times'] - gps_events['times'][0]
        imu_times_norm = imu_events['times'] - imu_events['times'][0]
        
        axes[1, 1].scatter(gps_times_norm, np.ones(len(gps_times_norm)), color='blue', s=50, alpha=0.7, label='GPS Events')
        
        if correlation_result:
            # Apply best offset to IMU times
            imu_times_corrected = imu_times_norm + correlation_result['best_offset']
            axes[1, 1].scatter(imu_times_corrected, np.ones(len(imu_times_corrected)) * 1.1, 
                             color='green', s=50, alpha=0.7, label='IMU Events (corrected)')
        
        axes[1, 1].set_xlabel('Time (seconds from start)')
        axes[1, 1].set_ylabel('Event Stream')
        axes[1, 1].set_title('Event Timing Comparison')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    # plt.savefig('event_based_sync_analysis.png', dpi=300, bbox_inches='tight')
    print(f"\n✅ Event-based analysis plot saved as 'event_based_sync_analysis.png'")
    
    return fig

def main():
    """Main analysis function."""
    print("=== EVENT-BASED TIME SYNCHRONIZATION ANALYSIS ===")
    print("Detecting physical events in GPS and IMU data for synchronization analysis...\n")

    # Load data
    print("Loading data...")
    imu_data = load_imu_data('../../pohang_dataset_005/navigation/ahrs.txt')    
    gps_data = load_gps_data('../../pohang_dataset_005/navigation/gps.txt')

    print(f"Loaded {len(imu_data):,} IMU samples and {len(gps_data):,} GPS samples\n")

    # Calculate sampling rates
    imu_times = imu_data[:, 0]
    gps_times = gps_data[:, 0]

    imu_dt = np.mean(np.diff(imu_times))
    gps_dt = np.mean(np.diff(gps_times))

    imu_rate = 1.0 / imu_dt
    gps_rate = 1.0 / gps_dt

    print(f"=== SAMPLING RATES ===")
    print(f"IMU: {imu_rate:.1f} Hz (interval: {imu_dt:.3f}s)")
    print(f"GPS: {gps_rate:.1f} Hz (interval: {gps_dt:.3f}s)")
    print()
    
    # Detect events
    print("=== DETECTING GPS EVENTS (Turns/Direction Changes) ===")
    gps_events = detect_gps_events(gps_data)
    
    print(f"\n=== DETECTING IMU EVENTS (Angular Rate Peaks) ===")
    imu_events = detect_imu_events(imu_data)
    
    # Cross-correlate events
    correlation_result = cross_correlate_events(gps_events, imu_events)
    
    # Create visualization
    plot_event_analysis(gps_events, imu_events, correlation_result)
    
    # Analyze offset acceptability based on GPS frequency
    print(f"\n=== OFFSET ACCEPTABILITY ANALYSIS ===")
    if correlation_result:
        offset = abs(correlation_result['best_offset'])
        gps_samples_in_offset = offset / gps_dt

        print(f"Time offset: {offset:.3f} seconds")
        print(f"GPS sampling interval: {gps_dt:.3f} seconds")
        print(f"Offset equivalent to: {gps_samples_in_offset:.1f} GPS samples")

        # Acceptability criteria
        if gps_samples_in_offset < 0.5:
            acceptability = "EXCELLENT - Less than half GPS sample"
            impact = "Negligible impact on fusion"
        elif gps_samples_in_offset < 1.0:
            acceptability = "GOOD - Less than one GPS sample"
            impact = "Minor impact, interpolation can handle"
        elif gps_samples_in_offset < 2.0:
            acceptability = "ACCEPTABLE - Less than two GPS samples"
            impact = "Moderate impact, correction recommended"
        elif gps_samples_in_offset < 5.0:
            acceptability = "POOR - Multiple GPS samples"
            impact = "Significant impact, correction required"
        else:
            acceptability = "UNACCEPTABLE - Many GPS samples"
            impact = "Severe impact, must correct"

        print(f"Acceptability: {acceptability}")
        print(f"Impact on fusion: {impact}")

        # Additional context for maritime navigation
        print(f"\n=== MARITIME NAVIGATION CONTEXT ===")
        vessel_speed_ms = 5.0  # Assume ~10 knots = 5 m/s typical for canal navigation
        position_error = offset * vessel_speed_ms
        print(f"At typical vessel speed (~5 m/s):")
        print(f"Position error due to offset: {position_error:.1f} meters")

        if position_error < 1.0:
            nav_impact = "Negligible for maritime navigation"
        elif position_error < 5.0:
            nav_impact = "Acceptable for open water, may affect canal navigation"
        elif position_error < 10.0:
            nav_impact = "Problematic for restricted waters"
        else:
            nav_impact = "Dangerous for canal/port navigation"

        print(f"Navigation impact: {nav_impact}")

    # Final recommendations
    print(f"\n=== RECOMMENDATIONS FOR INS/GPS FUSION ===")
    if correlation_result:
        if abs(correlation_result['best_offset']) > 0.1:
            print(f"⚠️  TIME OFFSET DETECTED: {correlation_result['best_offset']:.3f} seconds")
            print(f"   Apply correction: IMU_time_corrected = IMU_time + {correlation_result['best_offset']:.3f}")
        else:
            print(f"✅ Good time synchronization (offset: {correlation_result['best_offset']:.3f}s)")

        print(f"   Synchronization quality: {correlation_result['sync_quality']}")
        print(f"   Event matching rate: {correlation_result['best_score']*100:.1f}%")
    
    print(f"\n📋 NEXT STEPS:")
    print(f"1. If offset > 0.1s, apply time correction in your INS/GPS fusion")
    print(f"2. Monitor for time drift during long missions")
    print(f"3. Use event-based validation during fusion operation")
    print(f"4. Consider using corrected GPS data (gps_corrected.txt) if available")
    
    plt.show()

if __name__ == "__main__":
    main()
