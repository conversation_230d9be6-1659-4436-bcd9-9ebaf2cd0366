Pohang Canal Dataset
[A Multimodal Maritime Dataset for Autonomous Navigation in Restricted Waters]

A 12-person cruise boat (Aquapatio) with 7.9 m in length and 2.6 m in breadth was used for data collection
---------------------------------
Sensors:
Perceptual sensors: Two visual cameras as a stereo camera, an infrared camera, three 3D LiDARs, a radar, and an omnidirectional camera

Navigation sensors: Two GPS antennas connected to GNSS-RTK receiver and an AHRS

---------------------------------



## Sensor Specifications Table





-----

| Sensor Type | Manufacturer | Model | Location | Specification |
| :--- | :--- | :--- | :--- | :--- |
| GPS Antenna | SinoGNSS | AT340 | Front and back | RTK accuracy: 0.01m + 1ppm <br\> Heading accuracy: 0.28 deg |
| GNSS-RTK receiver | Synerex | TDR-3000 | - | RTK accuracy: 0.01m + 1ppm\<br\>Heading accuracy: 0.28° |
| AHRS | MicroStrain | 3DMGX5-AHRS | Back | **Acceleration**\<br\>Bias instability: 0.04mg\<br\>Resolution: 0.02mg\<br\>\<br\>**Gyroscope**\<br\>Bias instability: 8°/h\<br\>Resolution: 0.003°/s\<br\>\<br\>**Attitude measurement**\<br\>rms 0.5° (pitch, roll) |
| Visual Camera | Flir | Blackfly | Front | Resolution: 2048 X 1080 |
| Infrared Camera | Flir | A65 | Front | Resolution: 640 X 512\<br\>Temperature range: -25°C to 136°C |
| Omnidirectional Camera | Flir | Ladybug 5+ | Back | 6 cameras: 5 horizontal, 1 vertical\<br\>Resolution: 2464 X 2048 |
| LiDAR | Ouster | OS1-64 (gen1) | Front | 64 channel\<br\>hFOV: 360°, vFOV: 33.2°\<br\>Range: 0.5m to 120m\<br\>Resolution: 0.3cm\<br\>Precision: 1.0 to 5.0cm |
| LiDAR | Ouster | OS1-32 (gen1) | Port and starboard | 32 channel |
| Radar | Simrad | Broadband 4G™ Radar | Back | Frequency modulated continuous wave radar\<br\>X-band wavelength: 9.3GHz to 9.4GHz\<br\>Range: 50m to 1654.8m |

---------------------------------

The Dataset was collected in Pohang, South Korea, by following a 7.5 km long trajectory including a narrow canal area, inner/outer port area, and a near-coastal area.

---------------------------------
case study corner coordinates:
36°01'26.1"N 129°22'40.3"E

it is just to give us an idea about teh case study general locaton, not more than that. 

---------------------------------
Datset file format:


<data_name>
├── navigation
│   ├── ahrs.txt
│   ├── gps.txt
│   └── baseline.txt
├── lidar
│   ├── lidar_front
│   │   ├── imu.txt
│   │   └── points
│   │       └── <time>.bin
│   ├── lidar_port
│   │   ├── imu.txt
│   │   └── points
│   │       └── <time>.bin
│   └── lidar_starboard
│       ├── imu.txt
│       └── points
│           └── <time>.bin
├── stereo
│   ├── timestamp.txt
│   ├── left_images
│   │   └── <sequence>.png
│   └── right_images
│       └── <sequence>.png
├── infrared
│   ├── timestamp.txt
│   └── images
│       └── <sequence>.png
├── omni
│   ├── timestamp.txt
│   ├── cam_0
│   │   └── <sequence>.jpg
│   ├── cam_1
│   │   └── <sequence>.jpg
│   ├── cam_2
│   │   └── <sequence>.jpg
│   ├── cam_3
│   │   └── <sequence>.jpg
│   ├── cam_4
│   │   └── <sequence>.jpg
│   └── cam_5
│       └── <sequence>.jpg
├── radar
│   ├── timestamp.txt
│   ├── timestamp_deg.txt
│   └── images
│       └── <sequence>.png
└── calibration
    ├── extrinsics.json
    └── intrinsics.json



---------------------------------
All data are stored with acquisition times. The AHRS and GPS data are stored with the timestamp, images are named in sequence number and the corresponding timestamp is in timestamp.txt file, and LiDAR point cloud files are named in unix time (in nanoseconds).

---------------------------------
AHRS Format
All AHRS data in a single sequence are stored in one file as ahrs.txt in the navigation folder. 11 values as the following table are written in tab-delimited format. (The orientation is expressed as a quaternion)



| Unix time (s) | Orientation x | Orientation y | Orientation z | Orientation w | Angular rate x (rad/s) | Angular rate y (rad/s) | Angular rate z (rad/s) | Acceleration x (m/s²) | Acceleration y (m/s²) | Acceleration z (m/s²) |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |



---------------------------------
The baseline trajectory is stored as baseline.txt in the navigation folder for each dataset. Eight values as in the following table are written in a tab-delimited format for each timestamp. the coorinates are wihin the projection system UTM Zone 52N (EPSG:32652). 

| Unix time (s) | Orientation qx | Orientation qy | Orientation qz | Orientation qw | x coordinate (m) | y coordinate (m) | z coordinate (m) |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |



---------------------------------
GPS Format
All GPS signals in a single sequence are stored in one file as gps.txt in the navigation folder. Eleven values as in the following table are written in a tab-delimited format for each timestamp.


| Unix time (s) | GPS time (s) | Latitude (deg) | Hemisphere of latitude (N/S) | Longitude (deg) | Hemisphere of longitude (E/W) | Heading (deg) | GPS quality indicator | Number of satellites | Horizontal dilution of precision | Geoid height (m) |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |





---------------------------------
LiDAR Format:
The LiDAR data is written in binary files as following order:


| Item | x coordinate (m) | y coordinate (m) | z coordinate (m) | Intensity | Time (ns) | Reflectivity | Ambient | Range |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| Type | float | float | float | float | uint32 | uint16 | uint16 | uint32 |


---------------------------------
Reading LiDAR data on C++:
The code below shows an example of how to read the LiDAR point cloud binary file in c++
'''
std::ifstream is(<pointcloud_path>, std::ios::in | std::ios::binary);
while(is) {

    float x;

    float y;

    float z;

    float intensity;

    uint32_t time;	//nanosecond

    uint16_t reflectivity;

    uint16_t ambient;

    uint32_t range;

    is.read((char*)&x, sizeof(float));

    is.read((char*)&y, sizeof(float));

    is.read((char*)&z, sizeof(float));

    is.read((char*)&intensity, sizeof(float));

    is.read((char*)&time, sizeof(uint32_t));

    is.read((char*)&reflectivity, sizeof(uint16_t));

    is.read((char*)&ambient, sizeof(uint16_t));

    is.read((char*)&range, sizeof(uint32_t));

}

is.close();
'''

---------------------------------
Reading LiDAR data on Matlab
The 'fread' function in matlab can read one kind of format at once, so you might need to read each data type one by one.  We can read the data by using the 'skip' parameter in the 'fread' function. The function format is as : "fread(fileID,sizeA,precision,skip)"

The following code is an example of reading x, y, z coordinates from the binary file.

'''
fileID = fopen(lidar_path);

x = fread(fileID, inf, 'float', 24);

fseek(fileID, 4, 'bof');

y = fread(fileID, inf, 'float', 24);

fseek(fileID, 8, 'bof');

z = fread(fileID, inf, 'float', 24);

fseek(fileID, 12, 'bof');

intensity = fread(fileID, inf, 'float', 24);
'''

Since the total size of one point is 28 (4 + 4 + 4 + 4 + 4 + 2 + 2 + 4) bytes, the number '24' is the skip number to read float data (28 - 4 = 24). Also, to read the data which starts after some elements, skipping bytes of the previous elements can be done by 'fseek' function.

For example, the x coordinates start first, so it does not need 'fseek' function, but for y coordinates, it starts after the x coordinate, so skipping 4 bytes after the beginning of first (bof) is required before reading the data.

If you want to read 'reflectivity', the code is as follows:

'''
fseek(fileID, 20, 'bof');

reflectivity = fread(fileID, inf, 'uint16', 26);

'''

You can read the rest of the data (time, reflectivity, ...) using the same principle.


---------------------------------
Reading LiDAR data on Python Numpy
By setting the dtype following the binary file structure above, the "fromfile" function can load the lidar data. Code below shows an example of loading the point cloud binary file and getting the x, y, and z coordinates.
'''
lidar_dtype=[('x', np.float32), ('y', np.float32), ('z', np.float32), ('intensity', np.float32), ('time', np.uint32), ('reflectivity', np.uint16), ('ambient', np.uint16), ('range', np.uint32)]

scan = np.fromfile(<binary_file_path>, dtype=lidar_dtype)

points = np.stack((scan['x'], scan['y'], scan['z']), axis=-1)
'''

---------------------------------------

Timestamp for radar images


<Unix time> <image sequence> <start degree> <end degree>

The timestamp_deg.txt in radar folder contains the updated portion of the  radar image expressed in angles. The <Unix time> is the time when the updated radar signal has reached the <end degree>, image sequence is corresponding image in the images folder

----------------------------------------------
Calibration:
The intrinsics.json file contains the focal length, coordinates of the principal point, distortion coefficients, image height, and image width of each camera. The distortion coefficients are given as a vector containing five values, which follows the OpenCV representation.

----------------------------------------------
extrinsics.json
The extrinsic.json file contains the pose of each sensor in the AHRS coordinate system. The (x, y, z) coordinates are given as a vector, "translation", and the rotation is provided as a quaternion (qx, qy, qz, qw), "quaternion".

the pose is broken down into:

A "translation" vector for the (x, y, z) coordinates.

A "quaternion" for the rotation (qx, qy, qz, qw).

----------------------------------------------


navigation/ahrs.txt does not contain GPS-derived Latitude, Longitude, and Heading. Instead, its columns represent raw or pre-processed IMU sensor data. Specifically, the 11th column consistently contains values around -9.8 m/s², strongly indicating linear acceleration along the Z-axis (including gravity). The preceding columns (from the 2nd to the 10th, excluding the Unix time in the 1st) are likely corresponding angular rates and/or linear accelerations suitable for direct EKF integration. 

----------------------------------------------
Pohang00
2021. 07. 01. 16:25

Day/Night: Day

Data Length: 39 min. 33 sec.

Characteristics: GPS without RTK
----------------------------------------------

