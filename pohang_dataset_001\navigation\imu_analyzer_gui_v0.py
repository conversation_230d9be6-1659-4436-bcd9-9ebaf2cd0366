#!/usr/bin/env python3
"""
IMU/AHRS Data Interactive Analysis and Filtering Tool

This script analyzes IMU data from Pohang dataset and provides
interactive filtering capabilities with GUI controls.

Based on Data Structure.md:
AHRS Format (11 columns):
| Unix time (s) | Orientation x | Orientation y | Orientation z | Orientation w | 
| Angular rate x (rad/s) | Angular rate y (rad/s) | Angular rate z (rad/s) | 
| Acceleration x (m/s²) | Acceleration y (m/s²) | Acceleration z (m/s²) |
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import <PERSON>lider, Button, CheckButtons, RadioButtons
import pandas as pd
from pathlib import Path
import logging
from scipy.signal import butter, filtfilt, medfilt
from scipy.ndimage import uniform_filter1d
from scipy.stats import zscore
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from dataclasses import dataclass, field
import warnings

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import the preprocessing config from the module
@dataclass
class PreprocessingConfig:
    """Configuration for the Marine IMU Preprocessor."""
    # Master switch
    apply_preprocessing: bool = True

    # Stage 1: Spike Removal (Median Filter)
    apply_spike_removal: bool = True
    spike_kernel_size: int = 5

    # Stage 2: Low-Pass Filter (Butterworth)
    apply_low_pass: bool = True
    low_pass_cutoff_hz: float = 8.0

    # Stage 3: Smoothing (Moving Average) - Gyroscope only
    apply_smoothing: bool = True
    smoothing_window_size: int = 5

class IMUDataAnalyzer:
    def __init__(self, ahrs_file_path=None):
        """Initialize the IMU data analyzer"""
        self.ahrs_file_path = Path(ahrs_file_path) if ahrs_file_path else None
        self.original_data = None
        self.filtered_data = None
        self.sampling_rate = 100.0  # Default sampling rate
        
        # Data components
        self.component_names = {
            'orientation': ['Orientation X', 'Orientation Y', 'Orientation Z', 'Orientation W'],
            'gyroscope': ['Angular Rate X', 'Angular Rate Y', 'Angular Rate Z'],
            'accelerometer': ['Acceleration X', 'Acceleration Y', 'Acceleration Z']
        }
        
        # Current viewing component
        self.current_component = 'gyroscope'  # Start with gyroscope
        
        # Filter configurations for each component type
        self.filter_configs = {
            'orientation': PreprocessingConfig(
                apply_preprocessing=False,
                apply_spike_removal=False,
                spike_kernel_size=5,
                apply_low_pass=False,
                low_pass_cutoff_hz=5.0,
                apply_smoothing=False,
                smoothing_window_size=5
            ),
            'gyroscope': PreprocessingConfig(
                apply_preprocessing=False,
                apply_spike_removal=False,
                spike_kernel_size=5,
                apply_low_pass=False,
                low_pass_cutoff_hz=8.0,
                apply_smoothing=False,
                smoothing_window_size=5
            ),
            'accelerometer': PreprocessingConfig(
                apply_preprocessing=False,
                apply_spike_removal=False,
                spike_kernel_size=3,
                apply_low_pass=False,
                low_pass_cutoff_hz=10.0,
                apply_smoothing=False,
                smoothing_window_size=5
            )
        }
        
        if self.ahrs_file_path and self.ahrs_file_path.exists():
            self.load_ahrs_data()
    
    def load_ahrs_data(self):
        """Load AHRS data from file"""
        try:
            logger.info(f"Loading AHRS data from: {self.ahrs_file_path}")
            
            # Load data (tab-delimited, no header)
            data = pd.read_csv(self.ahrs_file_path, sep='\t', header=None)
            
            # Parse columns according to Data Structure.md
            self.original_data = {
                'unix_time': data.iloc[:, 0].values,
                'orientation_x': data.iloc[:, 1].values,
                'orientation_y': data.iloc[:, 2].values,
                'orientation_z': data.iloc[:, 3].values,
                'orientation_w': data.iloc[:, 4].values,
                'angular_rate_x': data.iloc[:, 5].values,
                'angular_rate_y': data.iloc[:, 6].values,
                'angular_rate_z': data.iloc[:, 7].values,
                'acceleration_x': data.iloc[:, 8].values,
                'acceleration_y': data.iloc[:, 9].values,
                'acceleration_z': data.iloc[:, 10].values
            }
            
            # Convert to relative time (start from 0)
            self.original_data['rel_time'] = (
                self.original_data['unix_time'] - self.original_data['unix_time'][0]
            )
            
            # Calculate sampling rate
            if len(self.original_data['unix_time']) > 1:
                time_diffs = np.diff(self.original_data['unix_time'])
                self.sampling_rate = 1.0 / np.median(time_diffs)
                logger.info(f"Estimated sampling rate: {self.sampling_rate:.1f} Hz")
            
            logger.info(f"Loaded {len(self.original_data['unix_time'])} IMU data points")
            logger.info(f"Duration: {self.original_data['rel_time'][-1]:.1f} seconds")
            
            # Initialize filtered data as copy of original
            self.filtered_data = self.original_data.copy()
            
        except Exception as e:
            logger.error(f"Error loading AHRS data: {e}")
            raise
    
    def get_component_data(self, component_type, data_dict):
        """Extract component data based on type"""
        if component_type == 'orientation':
            return np.column_stack([
                data_dict['orientation_x'],
                data_dict['orientation_y'],
                data_dict['orientation_z'],
                data_dict['orientation_w']
            ])
        elif component_type == 'gyroscope':
            return np.column_stack([
                data_dict['angular_rate_x'],
                data_dict['angular_rate_y'],
                data_dict['angular_rate_z']
            ])
        elif component_type == 'accelerometer':
            return np.column_stack([
                data_dict['acceleration_x'],
                data_dict['acceleration_y'],
                data_dict['acceleration_z']
            ])
    
    def set_component_data(self, component_type, data_dict, new_data):
        """Set component data based on type"""
        if component_type == 'orientation':
            data_dict['orientation_x'] = new_data[:, 0]
            data_dict['orientation_y'] = new_data[:, 1]
            data_dict['orientation_z'] = new_data[:, 2]
            data_dict['orientation_w'] = new_data[:, 3]
        elif component_type == 'gyroscope':
            data_dict['angular_rate_x'] = new_data[:, 0]
            data_dict['angular_rate_y'] = new_data[:, 1]
            data_dict['angular_rate_z'] = new_data[:, 2]
        elif component_type == 'accelerometer':
            data_dict['acceleration_x'] = new_data[:, 0]
            data_dict['acceleration_y'] = new_data[:, 1]
            data_dict['acceleration_z'] = new_data[:, 2]
    
    def remove_spikes(self, data, kernel_size=5):
        """Remove spikes using adaptive median filter"""
        filtered_data = data.copy()
        
        for i in range(data.shape[1]):
            # Apply median filter
            median_filtered = medfilt(data[:, i], kernel_size=kernel_size)
            
            # Calculate residuals
            residuals = np.abs(data[:, i] - median_filtered)
            threshold = np.percentile(residuals, 95)  # Adaptive threshold
            
            # Replace spikes with median values
            spike_mask = residuals > threshold * 3
            filtered_data[spike_mask, i] = median_filtered[spike_mask]
        
        return filtered_data
    
    def apply_lowpass(self, data, cutoff_hz, sampling_rate):
        """Apply low-pass Butterworth filter"""
        nyquist = sampling_rate / 2
        
        if cutoff_hz >= nyquist:
            logger.warning(f"Cutoff frequency {cutoff_hz} Hz is above Nyquist frequency {nyquist} Hz")
            return data
        
        # Design filter
        b, a = butter(N=4, Wn=cutoff_hz / nyquist, btype='low', analog=False)
        
        filtered_data = data.copy()
        for i in range(data.shape[1]):
            # Use zero-phase filtering (filtfilt) to avoid phase delay
            filtered_data[:, i] = filtfilt(b, a, data[:, i])
        
        return filtered_data
    
    def apply_smoothing(self, data, window_size=5):
        """Apply additional smoothing using moving average"""
        filtered_data = data.copy()
        
        for i in range(data.shape[1]):
            # Apply uniform filter (moving average)
            filtered_data[:, i] = uniform_filter1d(
                data[:, i], 
                size=window_size, 
                mode='nearest'
            )
        
        return filtered_data
    
    def apply_filters(self):
        """Apply filters to all components based on their configurations"""
        if self.original_data is None:
            return
        
        # Start with copy of original data
        self.filtered_data = self.original_data.copy()
        
        # Apply filters to each component type
        for component_type in ['orientation', 'gyroscope', 'accelerometer']:
            config = self.filter_configs[component_type]
            
            if not config.apply_preprocessing:
                continue
            
            # Get component data
            component_data = self.get_component_data(component_type, self.original_data)
            filtered_component = component_data.copy()
            
            # Apply filters in sequence
            if config.apply_spike_removal:
                logger.info(f"Applying spike removal to {component_type}")
                filtered_component = self.remove_spikes(
                    filtered_component, 
                    kernel_size=config.spike_kernel_size
                )
            
            if config.apply_low_pass:
                logger.info(f"Applying low-pass filter to {component_type}")
                filtered_component = self.apply_lowpass(
                    filtered_component,
                    cutoff_hz=config.low_pass_cutoff_hz,
                    sampling_rate=self.sampling_rate
                )
            
            if config.apply_smoothing and component_type == 'gyroscope':
                logger.info(f"Applying smoothing to {component_type}")
                filtered_component = self.apply_smoothing(
                    filtered_component,
                    window_size=config.smoothing_window_size
                )
            
            # Set filtered data back
            self.set_component_data(component_type, self.filtered_data, filtered_component)
    
    def analyze_component_statistics(self, component_type, data=None):
        """Analyze statistics for a component"""
        if data is None:
            data = self.original_data
        
        component_data = self.get_component_data(component_type, data)
        names = self.component_names[component_type]
        
        stats = {}
        for i, name in enumerate(names):
            signal = component_data[:, i]
            stats[name] = {
                'mean': np.mean(signal),
                'std': np.std(signal),
                'min': np.min(signal),
                'max': np.max(signal),
                'rms': np.sqrt(np.mean(signal**2))
            }
        
        return stats
    
    def create_interactive_gui(self):
        """Create interactive GUI for filter configuration"""
        self.fig, self.axes = plt.subplots(2, 2, figsize=(16, 10))
        self.fig.suptitle('IMU Data Interactive Filter Configuration', fontsize=16, fontweight='bold')
        
        # Make room for controls
        plt.subplots_adjust(bottom=0.35, right=0.82, left=0.05, top=0.93)
        
        # Create control panel
        self.create_control_panel()
        
        # Initial plot
        self.update_plots()
        
        plt.show()
        return self.fig
    
    def update_plots(self):
        """Update all plots with current filter settings"""
        if self.original_data is None:
            return
        
        # Clear axes
        for ax in self.axes.flat:
            ax.clear()
        
        # Apply current filters
        self.apply_filters()
        
        # Get current component data
        orig_data = self.get_component_data(self.current_component, self.original_data)
        filt_data = self.get_component_data(self.current_component, self.filtered_data)
        names = self.component_names[self.current_component]
        
        # Plot 1: Time series for all axes
        ax1 = self.axes[0, 0]
        colors = ['b', 'g', 'r', 'c']
        for i, name in enumerate(names):
            ax1.plot(self.original_data['rel_time'], orig_data[:, i],
                    colors[i] + '-', alpha=0.3, linewidth=1, label=f'{name} (Orig)')
            ax1.plot(self.filtered_data['rel_time'], filt_data[:, i],
                    colors[i] + '-', linewidth=1.5, label=f'{name} (Filt)')
        
        ax1.set_xlabel('Time (s)')
        ax1.set_ylabel(self._get_unit_label())
        ax1.set_title(f'{self.current_component.capitalize()} Time Series')
        ax1.grid(True, alpha=0.3)
        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
        
        # Plot 2: Power spectral density
        ax2 = self.axes[0, 1]
        for i, name in enumerate(names[:3]):  # Only first 3 for clarity
            # Compute PSD
            from scipy.signal import welch
            freqs, psd_orig = welch(orig_data[:, i], fs=self.sampling_rate, nperseg=256)
            freqs, psd_filt = welch(filt_data[:, i], fs=self.sampling_rate, nperseg=256)
            
            ax2.semilogy(freqs, psd_orig, colors[i] + '-', alpha=0.3, label=f'{name} (Orig)')
            ax2.semilogy(freqs, psd_filt, colors[i] + '-', linewidth=1.5, label=f'{name} (Filt)')
        
        ax2.set_xlabel('Frequency (Hz)')
        ax2.set_ylabel('PSD')
        ax2.set_title('Power Spectral Density')
        ax2.grid(True, alpha=0.3)
        ax2.legend(fontsize=8)
        ax2.set_xlim(0, self.sampling_rate/2)
        
        # Plot 3: Histogram comparison
        ax3 = self.axes[1, 0]
        # Show histogram for first axis only
        ax3.hist(orig_data[:, 0], bins=50, alpha=0.5, density=True, color='blue', label='Original')
        ax3.hist(filt_data[:, 0], bins=50, alpha=0.7, density=True, color='red', label='Filtered')
        ax3.set_xlabel(names[0])
        ax3.set_ylabel('Density')
        ax3.set_title(f'{names[0]} Distribution')
        ax3.grid(True, alpha=0.3)
        ax3.legend()
        
        # Plot 4: Statistics
        ax4 = self.axes[1, 1]
        ax4.axis('off')
        
        orig_stats = self.analyze_component_statistics(self.current_component, self.original_data)
        filt_stats = self.analyze_component_statistics(self.current_component, self.filtered_data)
        
        stats_text = f"Component: {self.current_component.upper()}\n"
        stats_text += f"Sampling Rate: {self.sampling_rate:.1f} Hz\n\n"
        
        config = self.filter_configs[self.current_component]
        stats_text += "Active Filters:\n"
        if config.apply_preprocessing:
            if config.apply_spike_removal:
                stats_text += f"✓ Spike Removal (kernel={config.spike_kernel_size})\n"
            if config.apply_low_pass:
                stats_text += f"✓ Low-pass ({config.low_pass_cutoff_hz:.1f} Hz)\n"
            if config.apply_smoothing and self.current_component == 'gyroscope':
                stats_text += f"✓ Smoothing (window={config.smoothing_window_size})\n"
        else:
            stats_text += "None\n"
        
        stats_text += "\nSignal Statistics:\n"
        for name in names[:3]:  # Show first 3 axes
            o_stat = orig_stats[name]
            f_stat = filt_stats[name]
            stats_text += f"\n{name}:\n"
            stats_text += f"  RMS: {o_stat['rms']:.4f} → {f_stat['rms']:.4f}\n"
            stats_text += f"  STD: {o_stat['std']:.4f} → {f_stat['std']:.4f}\n"
        
        ax4.text(0.05, 0.95, stats_text, transform=ax4.transAxes,
                fontfamily='monospace', fontsize=10, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
        
        plt.draw()
    
    def _get_unit_label(self):
        """Get appropriate unit label for current component"""
        if self.current_component == 'orientation':
            return 'Quaternion'
        elif self.current_component == 'gyroscope':
            return 'Angular Rate (rad/s)'
        elif self.current_component == 'accelerometer':
            return 'Acceleration (m/s²)'
    
    def create_control_panel(self):
        """Create control panel with component selection and filter controls"""
        
        # Component selector (radio buttons)
        component_ax = plt.axes([0.85, 0.7, 0.12, 0.15])
        self.component_selector = RadioButtons(
            component_ax, 
            ['Orientation', 'Gyroscope', 'Accelerometer'],
            active=1  # Start with Gyroscope
        )
        self.component_selector.on_clicked(self.on_component_changed)
        
        # Checkboxes for filter enable/disable
        checkbox_ax = plt.axes([0.02, 0.02, 0.15, 0.25])
        checkbox_labels = ['Enable Preprocessing', 'Spike Removal', 
                          'Low-pass Filter', 'Smoothing']
        self.checkboxes = CheckButtons(checkbox_ax, checkbox_labels, [False, False, False, False])
        self.checkboxes.on_clicked(self.on_checkbox_changed)
        
        # Sliders for parameters
        slider_x = 0.2
        slider_width = 0.25
        slider_height = 0.02
        
        # Spike kernel size slider
        spike_ax = plt.axes([slider_x, 0.22, slider_width, slider_height])
        self.spike_slider = Slider(spike_ax, 'Spike Kernel', 3, 15,
                                  valinit=5, valfmt='%d')
        
        # Low-pass cutoff slider
        cutoff_ax = plt.axes([slider_x, 0.18, slider_width, slider_height])
        self.cutoff_slider = Slider(cutoff_ax, 'Cutoff (Hz)', 0.1, 50.0,
                                   valinit=8.0)
        
        # Smoothing window slider
        smooth_ax = plt.axes([slider_x, 0.14, slider_width, slider_height])
        self.smooth_slider = Slider(smooth_ax, 'Smooth Window', 3, 21,
                                   valinit=5, valfmt='%d')
        
        # Buttons
        button_x = 0.5
        button_width = 0.12
        button_height = 0.04
        
        # Load file button
        load_ax = plt.axes([button_x, 0.22, button_width, button_height])
        self.load_button = Button(load_ax, 'Load AHRS File')
        self.load_button.on_clicked(self.on_load_file)
        
        # Update button
        update_ax = plt.axes([button_x, 0.17, button_width, button_height])
        self.update_button = Button(update_ax, 'Update Filters')
        self.update_button.on_clicked(self.on_update_filters)
        
        # Reset button
        reset_ax = plt.axes([button_x, 0.12, button_width, button_height])
        self.reset_button = Button(reset_ax, 'Reset Filters')
        self.reset_button.on_clicked(self.on_reset_filters)
        
        # Export button
        export_ax = plt.axes([button_x, 0.07, button_width, button_height])
        self.export_button = Button(export_ax, 'Export Filtered')
        self.export_button.on_clicked(self.on_export_data)
        
        # Apply to all components button
        apply_all_ax = plt.axes([button_x, 0.02, button_width, button_height])
        self.apply_all_button = Button(apply_all_ax, 'Apply to All')
        self.apply_all_button.on_clicked(self.on_apply_to_all)
        
        # Connect slider callbacks
        self.spike_slider.on_changed(self.on_slider_changed)
        self.cutoff_slider.on_changed(self.on_slider_changed)
        self.smooth_slider.on_changed(self.on_slider_changed)
        
        # Update initial checkbox states
        self.update_checkbox_states()
    
    def update_checkbox_states(self):
        """Update checkbox states to match current component config"""
        config = self.filter_configs[self.current_component]
        states = [
            config.apply_preprocessing,
            config.apply_spike_removal,
            config.apply_low_pass,
            config.apply_smoothing
        ]

        # Update checkboxes - set_active only takes index in newer matplotlib versions
        for i, state in enumerate(states):
            if self.checkboxes.get_status()[i] != state:
                self.checkboxes.set_active(i)
        
        # Update sliders
        self.spike_slider.set_val(config.spike_kernel_size)
        self.cutoff_slider.set_val(config.low_pass_cutoff_hz)
        self.smooth_slider.set_val(config.smoothing_window_size)
    
    def on_component_changed(self, label):
        """Handle component selection change"""
        self.current_component = label.lower()
        self.update_checkbox_states()
        self.update_plots()
    
    def on_checkbox_changed(self, label):
        """Handle checkbox changes"""
        config = self.filter_configs[self.current_component]
        
        checkbox_map = {
            'Enable Preprocessing': 'apply_preprocessing',
            'Spike Removal': 'apply_spike_removal',
            'Low-pass Filter': 'apply_low_pass',
            'Smoothing': 'apply_smoothing'
        }
        
        if label in checkbox_map:
            param_name = checkbox_map[label]
            # Get current state
            checkbox_index = list(checkbox_map.keys()).index(label)
            checkbox_state = self.checkboxes.get_status()[checkbox_index]
            
            # Update config
            setattr(config, param_name, checkbox_state)
            
            # Auto-update plots
            self.update_plots()
    
    def on_slider_changed(self, val):
        """Handle slider value changes"""
        config = self.filter_configs[self.current_component]
        config.spike_kernel_size = int(self.spike_slider.val)
        config.low_pass_cutoff_hz = self.cutoff_slider.val
        config.smoothing_window_size = int(self.smooth_slider.val)
    
    def on_update_filters(self, event):
        """Update filters when button is clicked"""
        self.update_plots()
    
    def on_reset_filters(self, event):
        """Reset all filters for current component"""
        config = self.filter_configs[self.current_component]
        config.apply_preprocessing = False
        config.apply_spike_removal = False
        config.apply_low_pass = False
        config.apply_smoothing = False
        config.spike_kernel_size = 5
        config.low_pass_cutoff_hz = 8.0
        config.smoothing_window_size = 5
        
        self.update_checkbox_states()
        self.update_plots()
    
    def on_apply_to_all(self, event):
        """Apply current component settings to all components"""
        current_config = self.filter_configs[self.current_component]
        
        # Ask for confirmation
        response = messagebox.askyesno(
            "Apply to All Components",
            f"Apply current {self.current_component} filter settings to all components?"
        )
        
        if response:
            for component in self.filter_configs:
                if component != self.current_component:
                    # Copy settings
                    config = self.filter_configs[component]
                    config.apply_preprocessing = current_config.apply_preprocessing
                    config.apply_spike_removal = current_config.apply_spike_removal
                    config.apply_low_pass = current_config.apply_low_pass
                    config.apply_smoothing = current_config.apply_smoothing
                    config.spike_kernel_size = current_config.spike_kernel_size
                    config.low_pass_cutoff_hz = current_config.low_pass_cutoff_hz
                    config.smoothing_window_size = current_config.smoothing_window_size
            
            self.update_plots()
            logger.info("Applied current settings to all components")
    
    def on_load_file(self, event):
        """Load new AHRS file using file dialog"""
        try:
            # Create file dialog
            root = tk.Tk()
            root.withdraw()
            
            # Open file dialog
            new_file_path = filedialog.askopenfilename(
                title="Select AHRS data file (ahrs.txt)",
                filetypes=[
                    ("Text files", "*.txt"),
                    ("All files", "*.*")
                ],
                initialdir=str(self.ahrs_file_path.parent) if self.ahrs_file_path else "."
            )
            
            root.destroy()
            
            if new_file_path:
                # Update file path and reload data
                self.ahrs_file_path = Path(new_file_path)
                logger.info(f"Loading new AHRS file: {self.ahrs_file_path}")
                
                # Reload data
                self.load_ahrs_data()
                
                # Update plots
                self.update_plots()
                
                # Show success message
                plt.figtext(0.7, 0.3, f"✓ Loaded:\n{self.ahrs_file_path.name}\n{len(self.original_data['unix_time'])} points",
                           bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8),
                           fontsize=10)
                plt.draw()
                
        except Exception as e:
            logger.error(f"Error loading AHRS file: {e}")
            plt.figtext(0.7, 0.3, f"✗ Load failed:\n{str(e)}",
                       bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8),
                       fontsize=10)
            plt.draw()
    
    def on_export_data(self, event):
        """Export filtered data to new file"""
        try:
            # Create file save dialog
            root = tk.Tk()
            root.withdraw()
            
            # Suggest default filename
            input_path = Path(self.ahrs_file_path)
            default_filename = f"{input_path.stem}_filtered{input_path.suffix}"
            
            # Open save dialog
            output_path = filedialog.asksaveasfilename(
                title="Save filtered AHRS data",
                defaultextension=".txt",
                filetypes=[
                    ("Text files", "*.txt"),
                    ("All files", "*.*")
                ],
                initialdir=str(input_path.parent),
                initialfile=default_filename
            )
            
            root.destroy()
            
            if output_path:
                output_path = Path(output_path)
                
                # Prepare data for export (same format as original)
                export_data = []
                
                for i in range(len(self.filtered_data['unix_time'])):
                    row = [
                        self.filtered_data['unix_time'][i],
                        self.filtered_data['orientation_x'][i],
                        self.filtered_data['orientation_y'][i],
                        self.filtered_data['orientation_z'][i],
                        self.filtered_data['orientation_w'][i],
                        self.filtered_data['angular_rate_x'][i],
                        self.filtered_data['angular_rate_y'][i],
                        self.filtered_data['angular_rate_z'][i],
                        self.filtered_data['acceleration_x'][i],
                        self.filtered_data['acceleration_y'][i],
                        self.filtered_data['acceleration_z'][i]
                    ]
                    export_data.append(row)
                
                # Save to file
                df_export = pd.DataFrame(export_data)
                df_export.to_csv(output_path, sep='\t', header=False, index=False, float_format='%.9f')
                
                logger.info(f"Filtered AHRS data exported to: {output_path}")
                
                # Show success message
                plt.figtext(0.7, 0.02, f"✓ Exported to:\n{output_path.name}",
                           bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8),
                           fontsize=10)
                plt.draw()
                
        except Exception as e:
            logger.error(f"Error exporting data: {e}")
            plt.figtext(0.7, 0.02, f"✗ Export failed:\n{str(e)}",
                       bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8),
                       fontsize=10)
            plt.draw()


def main():
    """Main function to run the IMU data analyzer"""
    
    print("🎛️ IMU/AHRS Data Interactive Analysis and Filtering Tool")
    print("=" * 60)
    
    # Use tkinter file dialog to select file
    root = tk.Tk()
    root.withdraw()
    
    ahrs_file_path = filedialog.askopenfilename(
        title="Select AHRS data file (ahrs.txt)",
        filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
        initialdir=str(Path.cwd())
    )
    
    root.destroy()
    
    if not ahrs_file_path:
        print("No file selected. Exiting...")
        return
    
    try:
        # Create analyzer
        print("🎛️ Starting interactive filtering GUI...")
        analyzer = IMUDataAnalyzer(ahrs_file_path)
        
        # Show interactive GUI
        analyzer.create_interactive_gui()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
