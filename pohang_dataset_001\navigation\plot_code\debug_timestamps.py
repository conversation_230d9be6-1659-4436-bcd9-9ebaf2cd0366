import numpy as np
import matplotlib.pyplot as plt

def analyze_timestamp_issues():
    """Analyze timestamp issues in GPS and baseline data."""
    
    print("=== TIMESTAMP ANALYSIS ===")
    
    # Load raw timestamps
    gps_times = []
    baseline_times = []
    
    # Load GPS timestamps
    with open('gps.txt', 'r') as f:
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 11:
                gps_times.append(float(parts[0]))
    
    # Load baseline timestamps
    with open('baseline.txt', 'r') as f:
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 8:
                baseline_times.append(float(parts[0]))
    
    gps_times = np.array(gps_times)
    baseline_times = np.array(baseline_times)
    
    print(f"GPS data: {len(gps_times)} points")
    print(f"Baseline data: {len(baseline_times)} points")
    
    # Calculate sampling rates
    gps_intervals = np.diff(gps_times)
    baseline_intervals = np.diff(baseline_times)
    
    print(f"\n=== SAMPLING RATE ANALYSIS ===")
    print(f"GPS sampling intervals:")
    print(f"  Mean: {np.mean(gps_intervals):.6f} seconds ({1/np.mean(gps_intervals):.1f} Hz)")
    print(f"  Min: {np.min(gps_intervals):.6f} seconds")
    print(f"  Max: {np.max(gps_intervals):.6f} seconds")
    print(f"  Std: {np.std(gps_intervals):.6f} seconds")
    
    print(f"\nBaseline sampling intervals:")
    print(f"  Mean: {np.mean(baseline_intervals):.6f} seconds ({1/np.mean(baseline_intervals):.1f} Hz)")
    print(f"  Min: {np.min(baseline_intervals):.6f} seconds")
    print(f"  Max: {np.max(baseline_intervals):.6f} seconds")
    print(f"  Std: {np.std(baseline_intervals):.6f} seconds")
    
    # Check for timestamp alignment
    print(f"\n=== TIMESTAMP ALIGNMENT CHECK ===")
    print(f"GPS first timestamp: {gps_times[0]:.6f}")
    print(f"GPS last timestamp: {gps_times[-1]:.6f}")
    print(f"Baseline first timestamp: {baseline_times[0]:.6f}")
    print(f"Baseline last timestamp: {baseline_times[-1]:.6f}")
    
    print(f"\nTime differences:")
    print(f"Start difference: {gps_times[0] - baseline_times[0]:.6f} seconds")
    print(f"End difference: {gps_times[-1] - baseline_times[-1]:.6f} seconds")
    
    # Look for exact timestamp matches
    print(f"\n=== EXACT TIMESTAMP MATCHES ===")
    exact_matches = 0
    close_matches_001 = 0
    close_matches_01 = 0
    
    for gps_time in gps_times[:1000]:  # Check first 1000 GPS points
        # Find closest baseline timestamp
        time_diffs = np.abs(baseline_times - gps_time)
        min_diff = np.min(time_diffs)
        
        if min_diff == 0:
            exact_matches += 1
        elif min_diff <= 0.001:
            close_matches_001 += 1
        elif min_diff <= 0.01:
            close_matches_01 += 1
    
    print(f"In first 1000 GPS points:")
    print(f"  Exact matches (0s): {exact_matches}")
    print(f"  Close matches (≤0.001s): {close_matches_001}")
    print(f"  Close matches (≤0.01s): {close_matches_01}")
    
    # Check if timestamps are actually identical
    print(f"\n=== TIMESTAMP SEQUENCE ANALYSIS ===")
    
    # Check first 20 timestamps of each
    print(f"First 10 GPS timestamps:")
    for i in range(min(10, len(gps_times))):
        print(f"  {i}: {gps_times[i]:.6f}")
    
    print(f"\nFirst 10 Baseline timestamps:")
    for i in range(min(10, len(baseline_times))):
        print(f"  {i}: {baseline_times[i]:.6f}")
    
    # Look for patterns
    print(f"\n=== PATTERN ANALYSIS ===")
    
    # Check if baseline timestamps are subset of GPS timestamps
    baseline_in_gps = 0
    for baseline_time in baseline_times[:100]:
        if np.any(np.abs(gps_times - baseline_time) < 1e-6):
            baseline_in_gps += 1
    
    print(f"Baseline timestamps found in GPS (first 100): {baseline_in_gps}/100")
    
    # Check if GPS timestamps are subset of baseline timestamps  
    gps_in_baseline = 0
    for gps_time in gps_times[:100]:
        if np.any(np.abs(baseline_times - gps_time) < 1e-6):
            gps_in_baseline += 1
    
    print(f"GPS timestamps found in baseline (first 100): {gps_in_baseline}/100")
    
    # Plot timestamp distributions
    plt.figure(figsize=(15, 10))
    
    # Plot 1: Sampling intervals
    plt.subplot(2, 3, 1)
    plt.hist(gps_intervals, bins=50, alpha=0.7, label='GPS', color='blue')
    plt.xlabel('Time Interval (s)')
    plt.ylabel('Frequency')
    plt.title('GPS Sampling Intervals')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(2, 3, 2)
    plt.hist(baseline_intervals, bins=50, alpha=0.7, label='Baseline', color='red')
    plt.xlabel('Time Interval (s)')
    plt.ylabel('Frequency')
    plt.title('Baseline Sampling Intervals')
    plt.grid(True, alpha=0.3)
    
    # Plot 3: Timeline comparison
    plt.subplot(2, 3, 3)
    time_range = min(1000, len(gps_times), len(baseline_times))
    plt.plot(gps_times[:time_range] - gps_times[0], 'b.', markersize=2, label='GPS')
    plt.plot(baseline_times[:time_range] - baseline_times[0], 'r.', markersize=2, label='Baseline')
    plt.xlabel('Point Index')
    plt.ylabel('Relative Time (s)')
    plt.title(f'Timeline Comparison (First {time_range} points)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot 4: Time differences between consecutive points
    plt.subplot(2, 3, 4)
    plt.plot(gps_intervals[:1000], 'b-', alpha=0.7, label='GPS')
    plt.xlabel('Point Index')
    plt.ylabel('Time Interval (s)')
    plt.title('GPS Time Intervals')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(2, 3, 5)
    plt.plot(baseline_intervals[:1000], 'r-', alpha=0.7, label='Baseline')
    plt.xlabel('Point Index')
    plt.ylabel('Time Interval (s)')
    plt.title('Baseline Time Intervals')
    plt.grid(True, alpha=0.3)
    
    # Plot 6: Timestamp alignment check
    plt.subplot(2, 3, 6)
    # Find time differences for first 1000 points
    time_diffs = []
    for i in range(min(1000, len(gps_times))):
        gps_time = gps_times[i]
        closest_baseline_idx = np.argmin(np.abs(baseline_times - gps_time))
        time_diff = gps_time - baseline_times[closest_baseline_idx]
        time_diffs.append(time_diff)
    
    plt.plot(time_diffs, 'g-', alpha=0.7)
    plt.xlabel('GPS Point Index')
    plt.ylabel('Time Difference (s)')
    plt.title('GPS vs Closest Baseline Time Difference')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('timestamp_analysis.png', dpi=300, bbox_inches='tight')
    print(f"\nTimestamp analysis plot saved as 'timestamp_analysis.png'")
    
    # CONCLUSION
    print(f"\n=== DIAGNOSIS ===")
    if exact_matches > 50:
        print("✅ Many exact timestamp matches found - timestamps appear synchronized")
    elif close_matches_001 > 50:
        print("⚠️ Close timestamp matches found - minor timing offset")
    elif close_matches_01 > 50:
        print("⚠️ Approximate timestamp matches - possible timing issues")
    else:
        print("❌ Very few close timestamp matches - major synchronization problem!")
    
    sampling_ratio = np.mean(gps_intervals) / np.mean(baseline_intervals)
    print(f"Sampling rate ratio (GPS/Baseline): {sampling_ratio:.2f}")
    
    if abs(sampling_ratio - 1.0) > 0.1:
        print("❌ Different sampling rates detected - this explains the gaps!")
    else:
        print("✅ Similar sampling rates")

if __name__ == "__main__":
    analyze_timestamp_issues()
