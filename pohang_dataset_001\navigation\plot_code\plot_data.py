import numpy as np
import matplotlib.pyplot as plt
from pyproj import Proj
import warnings
warnings.filterwarnings('ignore')

def load_gps_data(filename):
    """
    Load GPS data from file.
    Format: Unix time, GPS time, Latitude (deg), Hemisphere lat, Longitude (deg),
            Hemisphere lon, Heading (deg), GPS quality, Num satellites, HDOP, Geoid height
    """
    data = []
    with open(filename, 'r') as f:
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 11:
                unix_time = float(parts[0])
                lat = float(parts[2])
                lat_hem = parts[3]
                lon = float(parts[4])
                lon_hem = parts[5]

                # Apply hemisphere signs
                if lat_hem == 'S':
                    lat = -lat
                if lon_hem == 'W':
                    lon = -lon

                data.append([unix_time, lat, lon])

    return np.array(data)

def load_baseline_data(filename):
    """
    Load baseline data from file.
    Format: Unix time, qx, qy, qz, qw, x (m), y (m), z (m)
    """
    data = []
    with open(filename, 'r') as f:
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 8:
                unix_time = float(parts[0])
                x = float(parts[5])
                y = float(parts[6])
                z = float(parts[7])
                data.append([unix_time, x, y, z])

    return np.array(data)

def convert_gps_to_utm(lat_lon_data):
    """
    Convert GPS lat/lon coordinates to UTM coordinates.
    Based on baseline coordinates analysis, the baseline appears to be in UTM Zone 52N.
    """
    # Determine UTM zone for Pohang, South Korea (zone 52N)
    # Pohang coordinates: 36°01'26.1"N 129°22'40.3"E
    # Baseline coordinates suggest UTM 52N: X~3.98M (northing), Y~534K (easting)
    utm_zone = 52
    utm_proj = Proj(proj='utm', zone=utm_zone, ellps='WGS84', preserve_units=False)

    utm_coords = []
    for i in range(len(lat_lon_data)):
        lat, lon = lat_lon_data[i, 1], lat_lon_data[i, 2]
        x_utm, y_utm = utm_proj(lon, lat)
        utm_coords.append([lat_lon_data[i, 0], x_utm, y_utm])  # time, x_utm, y_utm

    return np.array(utm_coords)

def synchronize_trajectories(gps_data, baseline_data, time_tolerance=0.1):
    """
    Synchronize GPS and baseline data based on timestamps.
    """
    gps_sync = []
    baseline_sync = []

    for i, gps_time in enumerate(gps_data[:, 0]):
        # Find closest baseline timestamp
        time_diffs = np.abs(baseline_data[:, 0] - gps_time)
        min_idx = np.argmin(time_diffs)

        if time_diffs[min_idx] <= time_tolerance:
            gps_sync.append(gps_data[i])
            baseline_sync.append(baseline_data[min_idx])

    return np.array(gps_sync), np.array(baseline_sync)

def align_coordinate_systems(gps_utm, baseline_data):
    """
    Align the coordinate systems by finding the best translation and rotation.
    This is a simple alignment - for more complex cases, you might need
    more sophisticated registration algorithms.
    """
    # Extract coordinates
    gps_coords = gps_utm[:, 1:3]  # x, y from UTM
    baseline_coords = baseline_data[:, 1:3]  # x, y from baseline

    # Simple translation alignment (align centroids)
    gps_centroid = np.mean(gps_coords, axis=0)
    baseline_centroid = np.mean(baseline_coords, axis=0)

    # Translate baseline to align centroids
    translation = gps_centroid - baseline_centroid
    baseline_aligned = baseline_coords + translation

    return gps_coords, baseline_aligned, translation

def plot_trajectories(gps_coords, baseline_coords, title="GPS vs Baseline Trajectories"):
    """
    Plot both trajectories on the same figure.
    """
    plt.figure(figsize=(12, 10))

    # Plot trajectories
    plt.plot(gps_coords[:, 0], gps_coords[:, 1], 'b-', linewidth=2, label='GPS Trajectory', alpha=0.7)
    plt.plot(baseline_coords[:, 0], baseline_coords[:, 1], 'r-', linewidth=2, label='Baseline (Ground Truth)', alpha=0.7)

    # Mark start and end points
    plt.plot(gps_coords[0, 0], gps_coords[0, 1], 'bo', markersize=8, label='GPS Start')
    plt.plot(gps_coords[-1, 0], gps_coords[-1, 1], 'bs', markersize=8, label='GPS End')
    plt.plot(baseline_coords[0, 0], baseline_coords[0, 1], 'ro', markersize=8, label='Baseline Start')
    plt.plot(baseline_coords[-1, 0], baseline_coords[-1, 1], 'rs', markersize=8, label='Baseline End')

    plt.xlabel('X Coordinate (m)')
    plt.ylabel('Y Coordinate (m)')
    plt.title(title)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.axis('equal')
    plt.tight_layout()

    return plt.gcf()

def calculate_trajectory_errors(gps_coords, baseline_coords):
    """
    Calculate trajectory errors between GPS and baseline.
    """
    if len(gps_coords) != len(baseline_coords):
        min_len = min(len(gps_coords), len(baseline_coords))
        gps_coords = gps_coords[:min_len]
        baseline_coords = baseline_coords[:min_len]

    # Calculate Euclidean distances
    errors = np.sqrt(np.sum((gps_coords - baseline_coords)**2, axis=1))

    # Statistics
    mean_error = np.mean(errors)
    std_error = np.std(errors)
    max_error = np.max(errors)
    rmse = np.sqrt(np.mean(errors**2))

    return {
        'errors': errors,
        'mean_error': mean_error,
        'std_error': std_error,
        'max_error': max_error,
        'rmse': rmse
    }

def analyze_coordinate_systems(gps_data, baseline_data):
    """
    Analyze the coordinate systems to help identify the baseline projection.
    """
    print("\n=== Coordinate System Analysis ===")

    # GPS coordinates (lat/lon)
    lat_range = [np.min(gps_data[:, 1]), np.max(gps_data[:, 1])]
    lon_range = [np.min(gps_data[:, 2]), np.max(gps_data[:, 2])]
    print(f"GPS Latitude range: {lat_range[0]:.6f}° to {lat_range[1]:.6f}°")
    print(f"GPS Longitude range: {lon_range[0]:.6f}° to {lon_range[1]:.6f}°")

    # Baseline coordinates (assumed projected)
    x_range = [np.min(baseline_data[:, 1]), np.max(baseline_data[:, 1])]
    y_range = [np.min(baseline_data[:, 2]), np.max(baseline_data[:, 2])]
    z_range = [np.min(baseline_data[:, 3]), np.max(baseline_data[:, 3])]
    print(f"Baseline X range: {x_range[0]:.1f} to {x_range[1]:.1f} m")
    print(f"Baseline Y range: {y_range[0]:.1f} to {y_range[1]:.1f} m")
    print(f"Baseline Z range: {z_range[0]:.3f} to {z_range[1]:.3f} m")

    # Analysis
    print(f"\nCoordinate Analysis:")
    print(f"- X coordinates (~{x_range[0]/1e6:.1f}M) suggest UTM northing")
    print(f"- Y coordinates (~{y_range[0]/1e3:.0f}K) suggest UTM easting")
    print(f"- For Pohang (36°N, 129°E), this matches UTM Zone 52N")

    return lat_range, lon_range, x_range, y_range

def main():
    """
    Main function to load data, process, and plot trajectories.
    """
    print("Loading GPS and Baseline data...")

    # Load data
    gps_data = load_gps_data('gps.txt')
    baseline_data = load_baseline_data('baseline.txt')

    print(f"Loaded {len(gps_data)} GPS points and {len(baseline_data)} baseline points")

    # Analyze coordinate systems
    analyze_coordinate_systems(gps_data, baseline_data)

    # Convert GPS to UTM coordinates
    print("Converting GPS coordinates to UTM...")
    gps_utm = convert_gps_to_utm(gps_data)

    # Synchronize trajectories
    print("Synchronizing trajectories...")
    gps_sync, baseline_sync = synchronize_trajectories(gps_utm, baseline_data)

    print(f"Synchronized {len(gps_sync)} points")

    # Align coordinate systems
    print("Aligning coordinate systems...")
    gps_coords, baseline_aligned, translation = align_coordinate_systems(gps_sync, baseline_sync)

    print(f"Applied translation: {translation}")

    # Plot trajectories
    print("Plotting trajectories...")
    fig = plot_trajectories(gps_coords, baseline_aligned,
                          "GPS vs Baseline Trajectories (Aligned)")

    # Calculate and display errors
    error_stats = calculate_trajectory_errors(gps_coords, baseline_aligned)

    print("\n=== Trajectory Error Statistics ===")
    print(f"Mean Error: {error_stats['mean_error']:.3f} m")
    print(f"Std Error: {error_stats['std_error']:.3f} m")
    print(f"Max Error: {error_stats['max_error']:.3f} m")
    print(f"RMSE: {error_stats['rmse']:.3f} m")

    # Plot error over time
    plt.figure(figsize=(12, 6))
    time_sync = gps_sync[:len(error_stats['errors']), 0]
    time_relative = (time_sync - time_sync[0]) / 60  # Convert to minutes

    plt.plot(time_relative, error_stats['errors'], 'g-', linewidth=1)
    plt.xlabel('Time (minutes)')
    plt.ylabel('Position Error (m)')
    plt.title('GPS vs Baseline Position Error Over Time')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()

    # Save plots
    fig.savefig('trajectories_comparison.png', dpi=300, bbox_inches='tight')
    plt.gcf().savefig('position_errors.png', dpi=300, bbox_inches='tight')

    print("\nPlots saved as 'trajectories_comparison.png' and 'position_errors.png'")

    plt.show()

if __name__ == "__main__":
    main()