import numpy as np
import matplotlib.pyplot as plt

def load_gps_data(filename):
    """Load GPS data and convert to approximate local coordinates."""
    data = []
    with open(filename, 'r') as f:
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 11:
                unix_time = float(parts[0])
                lat = float(parts[2])
                lat_hem = parts[3]
                lon = float(parts[4])
                lon_hem = parts[5]
                
                # Apply hemisphere signs
                if lat_hem == 'S':
                    lat = -lat
                if lon_hem == 'W':
                    lon = -lon
                    
                data.append([unix_time, lat, lon])
    
    return np.array(data)

def load_baseline_data(filename):
    """Load baseline data."""
    data = []
    with open(filename, 'r') as f:
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 8:
                unix_time = float(parts[0])
                x = float(parts[5])
                y = float(parts[6])
                z = float(parts[7])
                data.append([unix_time, x, y, z])
    
    return np.array(data)

def lat_lon_to_utm_52n(lat_lon_data):
    """Convert lat/lon to UTM Zone 52N coordinates (simplified)."""
    # UTM Zone 52N parameters
    central_meridian = 129.0
    
    # Earth parameters (WGS84)
    a = 6378137.0  # Semi-major axis
    f = 1/298.257223563  # Flattening
    e2 = 2*f - f*f  # First eccentricity squared
    
    # UTM scale factor and false northing/easting
    k0 = 0.9996
    false_easting = 500000.0
    false_northing = 0.0  # Northern hemisphere
    
    utm_coords = []
    
    for i in range(len(lat_lon_data)):
        lat = lat_lon_data[i, 1]
        lon = lat_lon_data[i, 2]
        
        # Convert to radians
        lat_rad = np.radians(lat)
        lon_rad = np.radians(lon)
        central_meridian_rad = np.radians(central_meridian)
        
        # Calculate UTM coordinates (simplified)
        delta_lon = lon_rad - central_meridian_rad
        
        # Simplified UTM formulas (good for small areas)
        N = a / np.sqrt(1 - e2 * np.sin(lat_rad)**2)
        T = np.tan(lat_rad)**2
        C = e2 * np.cos(lat_rad)**2 / (1 - e2)
        A = np.cos(lat_rad) * delta_lon
        
        # Calculate M (meridional arc)
        M = a * ((1 - e2/4 - 3*e2**2/64) * lat_rad - 
                 (3*e2/8 + 3*e2**2/32) * np.sin(2*lat_rad) + 
                 (15*e2**2/256) * np.sin(4*lat_rad))
        
        # UTM coordinates
        x_utm = k0 * N * (A + (1-T+C)*A**3/6 + (5-18*T+T**2+72*C)*A**5/120)
        y_utm = k0 * (M + N*np.tan(lat_rad)*(A**2/2 + (5-T+9*C+4*C**2)*A**4/24 + 
                                             (61-58*T+T**2+600*C)*A**6/720))
        
        # Apply false easting and northing
        easting = x_utm + false_easting
        northing = y_utm + false_northing
        
        utm_coords.append([lat_lon_data[i, 0], easting, northing])
    
    return np.array(utm_coords)

def convert_gps_to_baseline_coordinate_system(gps_utm_data):
    """Convert GPS UTM coordinates to the same coordinate system as baseline."""
    converted_coords = []
    
    for i in range(len(gps_utm_data)):
        utm_easting = gps_utm_data[i, 1]
        utm_northing = gps_utm_data[i, 2]
        
        # Apply the transformation we discovered:
        baseline_x = utm_northing  # UTM northing becomes baseline X
        baseline_y = utm_easting   # UTM easting becomes baseline Y
        
        converted_coords.append([gps_utm_data[i, 0], baseline_x, baseline_y])
    
    return np.array(converted_coords)

def synchronize_trajectories(gps_data, baseline_data, time_tolerance=0.1):
    """Synchronize GPS and baseline data based on timestamps."""
    gps_sync = []
    baseline_sync = []
    
    for i, gps_time in enumerate(gps_data[:, 0]):
        # Find closest baseline timestamp
        time_diffs = np.abs(baseline_data[:, 0] - gps_time)
        min_idx = np.argmin(time_diffs)
        
        if time_diffs[min_idx] <= time_tolerance:
            gps_sync.append(gps_data[i])
            baseline_sync.append(baseline_data[min_idx])
    
    return np.array(gps_sync), np.array(baseline_sync)

def debug_error_calculation():
    """Debug the error calculation to see what's happening."""
    print("Loading data...")
    gps_data = load_gps_data('gps.txt')
    baseline_data = load_baseline_data('baseline.txt')

    print(f"GPS data: {len(gps_data)} points")
    print(f"GPS time range: {gps_data[0, 0]:.3f} to {gps_data[-1, 0]:.3f}")
    print(f"Baseline data: {len(baseline_data)} points")
    print(f"Baseline time range: {baseline_data[0, 0]:.3f} to {baseline_data[-1, 0]:.3f}")

    # Check time alignment
    print(f"\nTime difference at start: {gps_data[0, 0] - baseline_data[0, 0]:.6f} seconds")
    print(f"Time difference at end: {gps_data[-1, 0] - baseline_data[-1, 0]:.6f} seconds")

    print("Converting GPS to baseline coordinate system...")
    gps_utm = lat_lon_to_utm_52n(gps_data)
    gps_baseline_coords = convert_gps_to_baseline_coordinate_system(gps_utm)

    print("Synchronizing trajectories...")
    gps_sync, baseline_sync = synchronize_trajectories(gps_baseline_coords, baseline_data)

    print(f"Synchronized {len(gps_sync)} points")

    # Check time synchronization quality
    print(f"\n=== Time Synchronization Analysis ===")
    time_diffs = []
    for i in range(len(gps_sync)):
        time_diff = abs(gps_sync[i, 0] - baseline_sync[i, 0])
        time_diffs.append(time_diff)

    time_diffs = np.array(time_diffs)
    print(f"Time synchronization errors:")
    print(f"  Mean: {np.mean(time_diffs):.6f} seconds")
    print(f"  Max: {np.max(time_diffs):.6f} seconds")
    print(f"  Std: {np.std(time_diffs):.6f} seconds")
    print(f"  Points with >0.05s error: {np.sum(time_diffs > 0.05)}")
    print(f"  Points with >0.1s error: {np.sum(time_diffs > 0.1)}")

    # Extract coordinates
    gps_coords = gps_sync[:, 1:3]
    baseline_coords = baseline_sync[:, 1:3]

    print(f"GPS coords shape: {gps_coords.shape}")
    print(f"Baseline coords shape: {baseline_coords.shape}")

    # Apply translation
    gps_center = np.mean(gps_coords, axis=0)
    baseline_center = np.mean(baseline_coords, axis=0)
    translation = gps_center - baseline_center
    baseline_aligned = baseline_coords + translation

    print(f"Translation applied: {translation}")
    
    # Calculate errors step by step
    print("\n=== Debugging Error Calculation ===")
    
    # Check if arrays have same length
    print(f"GPS coords length: {len(gps_coords)}")
    print(f"Baseline aligned length: {len(baseline_aligned)}")
    
    if len(gps_coords) == len(baseline_aligned):
        # Calculate differences
        diff = gps_coords - baseline_aligned
        print(f"Difference array shape: {diff.shape}")
        print(f"First 5 differences:\n{diff[:5]}")
        
        # Calculate squared differences
        squared_diff = diff**2
        print(f"First 5 squared differences:\n{squared_diff[:5]}")
        
        # Sum along axis 1 (x and y components)
        sum_squared = np.sum(squared_diff, axis=1)
        print(f"First 5 sum of squared differences: {sum_squared[:5]}")
        
        # Take square root to get Euclidean distances
        errors = np.sqrt(sum_squared)
        print(f"First 5 errors: {errors[:5]}")
        
        # Statistics
        print(f"\nError Statistics:")
        print(f"Mean Error: {np.mean(errors):.3f} m")
        print(f"Std Error: {np.std(errors):.3f} m")
        print(f"Min Error: {np.min(errors):.3f} m")
        print(f"Max Error: {np.max(errors):.3f} m")
        print(f"RMSE: {np.sqrt(np.mean(errors**2)):.3f} m")
        
        # Check coordinate ranges to see if they make sense
        print(f"\n=== Coordinate Ranges ===")
        print(f"GPS X range: {np.min(gps_coords[:, 0]):.1f} to {np.max(gps_coords[:, 0]):.1f}")
        print(f"GPS Y range: {np.min(gps_coords[:, 1]):.1f} to {np.max(gps_coords[:, 1]):.1f}")
        print(f"Baseline X range: {np.min(baseline_aligned[:, 0]):.1f} to {np.max(baseline_aligned[:, 0]):.1f}")
        print(f"Baseline Y range: {np.min(baseline_aligned[:, 1]):.1f} to {np.max(baseline_aligned[:, 1]):.1f}")

        # Check for systematic bias
        print(f"\n=== Systematic Bias Analysis ===")
        x_bias = np.mean(diff[:, 0])
        y_bias = np.mean(diff[:, 1])
        print(f"Mean X bias: {x_bias:.3f} m")
        print(f"Mean Y bias: {y_bias:.3f} m")
        print(f"Overall bias magnitude: {np.sqrt(x_bias**2 + y_bias**2):.3f} m")

        # Check GPS accuracy from the raw data
        print(f"\n=== GPS Quality Analysis ===")
        # Load raw GPS data to check accuracy values
        gps_raw = load_gps_data('gps.txt')
        print(f"GPS data has {len(gps_raw)} total points")

        # Check if we can extract accuracy info from GPS file
        with open('gps.txt', 'r') as f:
            first_line = f.readline().strip()
            parts = first_line.split('\t')
            if len(parts) >= 11:
                print(f"GPS data format: {len(parts)} columns")
                print(f"Sample GPS line: {first_line}")
                # Column 10 might be horizontal accuracy
                print(f"Checking GPS accuracy values...")

        # Try to extract accuracy values
        accuracies = []
        with open('gps.txt', 'r') as f:
            for line in f:
                parts = line.strip().split('\t')
                if len(parts) >= 11:
                    try:
                        acc = float(parts[10])  # Assuming column 10 is accuracy
                        accuracies.append(acc)
                    except:
                        pass

        if accuracies:
            accuracies = np.array(accuracies)
            print(f"GPS accuracy statistics:")
            print(f"  Mean accuracy: {np.mean(accuracies):.3f} m")
            print(f"  Std accuracy: {np.std(accuracies):.3f} m")
            print(f"  Min accuracy: {np.min(accuracies):.3f} m")
            print(f"  Max accuracy: {np.max(accuracies):.3f} m")
        
        # Plot a small sample to visualize
        plt.figure(figsize=(12, 5))
        
        # Plot first 100 points
        n_sample = min(1000, len(gps_coords))
        
        plt.subplot(1, 2, 1)
        plt.plot(gps_coords[:n_sample, 0], gps_coords[:n_sample, 1], 'b.-', label='GPS', markersize=3)
        plt.plot(baseline_aligned[:n_sample, 0], baseline_aligned[:n_sample, 1], 'r.-', label='Baseline', markersize=3)
        plt.xlabel('X (m)')
        plt.ylabel('Y (m)')
        plt.title(f'First {n_sample} Points Comparison')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.axis('equal')
        
        plt.subplot(1, 2, 2)
        plt.plot(errors[:n_sample], 'g.-', markersize=3)
        plt.xlabel('Point Index')
        plt.ylabel('Error (m)')
        plt.title(f'Errors for First {n_sample} Points')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('debug_error_analysis.png', dpi=300, bbox_inches='tight')
        print(f"\nDebug plot saved as 'debug_error_analysis.png'")
        
        plt.show()

if __name__ == "__main__":
    debug_error_calculation()
