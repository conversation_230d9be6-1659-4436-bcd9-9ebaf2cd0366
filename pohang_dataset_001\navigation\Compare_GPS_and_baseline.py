import numpy as np
import matplotlib.pyplot as plt
import tkinter as tk
from tkinter import filedialog, messagebox
import os

def load_gps_data(filename):
    """Load GPS data and convert to approximate local coordinates."""
    data = []
    with open(filename, 'r') as f:
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 11:
                unix_time = float(parts[0])
                lat = float(parts[2])
                lat_hem = parts[3]
                lon = float(parts[4])
                lon_hem = parts[5]
                
                # Apply hemisphere signs
                if lat_hem == 'S':
                    lat = -lat
                if lon_hem == 'W':
                    lon = -lon
                    
                data.append([unix_time, lat, lon])
    
    return np.array(data)

def load_baseline_data(filename):
    """Load baseline data."""
    data = []
    with open(filename, 'r') as f:
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 8:
                unix_time = float(parts[0])
                x = float(parts[5])
                y = float(parts[6])
                z = float(parts[7])
                data.append([unix_time, x, y, z])
    
    return np.array(data)

def lat_lon_to_utm_52n(lat_lon_data):
    """
    Convert lat/lon to UTM Zone 52N coordinates (same system as baseline).
    This uses a simplified UTM conversion suitable for South Korea.
    """
    # UTM Zone 52N parameters
    # Central meridian: 129°E (perfect for Pohang at 129.37-129.38°E)
    central_meridian = 129.0

    # Earth parameters (WGS84)
    a = 6378137.0  # Semi-major axis
    f = 1/298.257223563  # Flattening
    e2 = 2*f - f*f  # First eccentricity squared

    # UTM scale factor and false northing/easting
    k0 = 0.9996
    false_easting = 500000.0
    false_northing = 0.0  # Northern hemisphere

    utm_coords = []

    for i in range(len(lat_lon_data)):
        lat = lat_lon_data[i, 1]
        lon = lat_lon_data[i, 2]

        # Convert to radians
        lat_rad = np.radians(lat)
        lon_rad = np.radians(lon)
        central_meridian_rad = np.radians(central_meridian)

        # Calculate UTM coordinates (simplified)
        delta_lon = lon_rad - central_meridian_rad

        # Simplified UTM formulas (good for small areas)
        N = a / np.sqrt(1 - e2 * np.sin(lat_rad)**2)
        T = np.tan(lat_rad)**2
        C = e2 * np.cos(lat_rad)**2 / (1 - e2)
        A = np.cos(lat_rad) * delta_lon

        # Calculate M (meridional arc)
        M = a * ((1 - e2/4 - 3*e2**2/64) * lat_rad -
                 (3*e2/8 + 3*e2**2/32) * np.sin(2*lat_rad) +
                 (15*e2**2/256) * np.sin(4*lat_rad))

        # UTM coordinates
        x_utm = k0 * N * (A + (1-T+C)*A**3/6 + (5-18*T+T**2+72*C)*A**5/120)
        y_utm = k0 * (M + N*np.tan(lat_rad)*(A**2/2 + (5-T+9*C+4*C**2)*A**4/24 +
                                             (61-58*T+T**2+600*C)*A**6/720))

        # Apply false easting and northing
        easting = x_utm + false_easting
        northing = y_utm + false_northing

        utm_coords.append([lat_lon_data[i, 0], easting, northing])

    return np.array(utm_coords)

def convert_gps_to_baseline_coordinate_system(gps_utm_data):
    """
    Convert GPS UTM coordinates to the same coordinate system as baseline.
    Based on our analysis: X flip: -1, Y flip: 1, Rotation: 267.4° ≈ 270°
    This suggests baseline uses: X = -UTM_Northing, Y = UTM_Easting
    """
    converted_coords = []

    for i in range(len(gps_utm_data)):
        utm_easting = gps_utm_data[i, 1]
        utm_northing = gps_utm_data[i, 2]

        # Apply the transformation we discovered:
        # Rotation of 267.4° ≈ 270° (counter-clockwise) + X flip
        # This is equivalent to: X_baseline = -Y_utm, Y_baseline = X_utm
        baseline_x = utm_northing  # UTM northing becomes baseline X
        baseline_y = utm_easting   # UTM easting becomes baseline Y

        converted_coords.append([gps_utm_data[i, 0], baseline_x, baseline_y])

    return np.array(converted_coords)

# Time shift detection function removed - no longer needed with spatial matching

def correct_gps_timestamps_by_spatial_matching(gps_data, baseline_data):
    """
    Correct GPS timestamps by finding spatially nearest baseline points
    and assigning their timestamps to GPS points.

    This approach assumes spatial trajectories are correct but timestamps may have
    offset or drift. Each GPS point gets the timestamp of its nearest baseline point.

    Args:
        gps_data: GPS data array [time, x, y]
        baseline_data: Baseline data array [time, x, y, z]

    Returns:
        corrected_gps_data: GPS data with corrected timestamps [new_time, x, y]
        matching_stats: Dictionary with matching statistics
    """
    print(f"\n=== CORRECTING GPS TIMESTAMPS BY SPATIAL MATCHING ===")
    print(f"GPS points: {len(gps_data)}")
    print(f"Baseline points: {len(baseline_data)}")

    corrected_gps_data = []
    spatial_distances = []
    time_corrections = []

    # Extract baseline coordinates (x, y only, ignore z)
    baseline_coords = baseline_data[:, 1:3]

    print("Finding mutual nearest neighbors (two-way matching)...")

    # Vectorized approach for better performance
    gps_coords = gps_data[:, 1:3]  # All GPS coordinates
    original_gps_times = gps_data[:, 0]  # All GPS timestamps

    # Step 1: For each GPS point, find nearest baseline point
    print("  Step 1: GPS → Baseline nearest neighbors...")
    gps_to_baseline = []
    gps_to_baseline_distances = []

    chunk_size = 1000
    for start_idx in range(0, len(gps_data), chunk_size):
        end_idx = min(start_idx + chunk_size, len(gps_data))
        print(f"    Processing GPS points {start_idx} to {end_idx-1}/{len(gps_data)}")

        gps_chunk = gps_coords[start_idx:end_idx]
        distances_chunk = np.sqrt(np.sum((gps_chunk[:, np.newaxis, :] - baseline_coords[np.newaxis, :, :])**2, axis=2))

        nearest_idx_chunk = np.argmin(distances_chunk, axis=1)
        nearest_dist_chunk = np.min(distances_chunk, axis=1)

        gps_to_baseline.extend(nearest_idx_chunk)
        gps_to_baseline_distances.extend(nearest_dist_chunk)

    gps_to_baseline = np.array(gps_to_baseline)
    gps_to_baseline_distances = np.array(gps_to_baseline_distances)

    # Step 2: For each baseline point, find nearest GPS point
    print("  Step 2: Baseline → GPS nearest neighbors...")
    baseline_to_gps = []
    baseline_to_gps_distances = []

    for start_idx in range(0, len(baseline_data), chunk_size):
        end_idx = min(start_idx + chunk_size, len(baseline_data))
        print(f"    Processing baseline points {start_idx} to {end_idx-1}/{len(baseline_data)}")

        baseline_chunk = baseline_coords[start_idx:end_idx]
        distances_chunk = np.sqrt(np.sum((baseline_chunk[:, np.newaxis, :] - gps_coords[np.newaxis, :, :])**2, axis=2))

        nearest_idx_chunk = np.argmin(distances_chunk, axis=1)
        nearest_dist_chunk = np.min(distances_chunk, axis=1)

        baseline_to_gps.extend(nearest_idx_chunk)
        baseline_to_gps_distances.extend(nearest_dist_chunk)

    baseline_to_gps = np.array(baseline_to_gps)
    baseline_to_gps_distances = np.array(baseline_to_gps_distances)

    # Step 3: Find mutual nearest neighbors
    print("  Step 3: Finding mutual nearest neighbors...")
    mutual_matches = []
    mutual_distances = []

    for gps_idx in range(len(gps_data)):
        baseline_idx = gps_to_baseline[gps_idx]  # GPS point's nearest baseline

        # Check if this baseline point's nearest GPS is the same GPS point
        if baseline_to_gps[baseline_idx] == gps_idx:
            # Mutual match found!
            mutual_matches.append((gps_idx, baseline_idx))
            mutual_distances.append(gps_to_baseline_distances[gps_idx])

    print(f"  Found {len(mutual_matches)} mutual nearest neighbor pairs out of {len(gps_data)} GPS points")
    print(f"  Match rate: {len(mutual_matches)/len(gps_data)*100:.1f}%")

    # Create corrected GPS data for mutual matches only
    corrected_gps_data = []
    gps_indices = []  # Track original GPS indices
    spatial_distances = []
    time_corrections = []

    for gps_idx, baseline_idx in mutual_matches:
        # Get corrected timestamp from baseline
        corrected_timestamp = baseline_data[baseline_idx, 0]
        gps_coords_point = gps_coords[gps_idx]
        original_time = original_gps_times[gps_idx]

        corrected_gps_data.append([corrected_timestamp, gps_coords_point[0], gps_coords_point[1]])
        gps_indices.append(gps_idx)  # Store original GPS index
        spatial_distances.append(gps_to_baseline_distances[gps_idx])
        time_corrections.append(corrected_timestamp - original_time)

    corrected_gps_data = np.array(corrected_gps_data)
    gps_indices = np.array(gps_indices)
    spatial_distances = np.array(spatial_distances)
    time_corrections = np.array(time_corrections)

# Arrays are already numpy arrays from vectorized operations

    # Calculate matching statistics
    matching_stats = {
        'mean_spatial_distance': np.mean(spatial_distances),
        'median_spatial_distance': np.median(spatial_distances),
        'max_spatial_distance': np.max(spatial_distances),
        'std_spatial_distance': np.std(spatial_distances),
        'mean_time_correction': np.mean(time_corrections),
        'median_time_correction': np.median(time_corrections),
        'std_time_correction': np.std(time_corrections),
        'min_time_correction': np.min(time_corrections),
        'max_time_correction': np.max(time_corrections)
    }

    print(f"\n=== SPATIAL MATCHING RESULTS ===")
    print(f"Spatial distances to nearest baseline points:")
    print(f"  Mean: {matching_stats['mean_spatial_distance']:.3f} m")
    print(f"  Median: {matching_stats['median_spatial_distance']:.3f} m")
    print(f"  Max: {matching_stats['max_spatial_distance']:.3f} m")
    print(f"  Std: {matching_stats['std_spatial_distance']:.3f} m")

    print(f"\nTime corrections applied:")
    print(f"  Mean correction: {matching_stats['mean_time_correction']:.6f} seconds")
    print(f"  Median correction: {matching_stats['median_time_correction']:.6f} seconds")
    print(f"  Std correction: {matching_stats['std_time_correction']:.6f} seconds")
    print(f"  Range: {matching_stats['min_time_correction']:.6f} to {matching_stats['max_time_correction']:.6f} seconds")

    if matching_stats['mean_spatial_distance'] < 1.0:
        print("✅ Excellent spatial matching - trajectories are well aligned")
    elif matching_stats['mean_spatial_distance'] < 5.0:
        print("✅ Good spatial matching")
    else:
        print("⚠️ Large spatial distances - check coordinate transformation")

    print(f"✅ GPS timestamps corrected using spatial nearest neighbor matching")

    return corrected_gps_data, gps_indices, matching_stats

def export_corrected_gps_data(corrected_gps_data, gps_indices, gps_file_path, output_filename="gps_corrected.txt"):
    """
    Export corrected GPS data maintaining original GPS file structure with corrected timestamps.

    Args:
        corrected_gps_data: Corrected GPS data array [corrected_time, x, y]
        gps_indices: Array of original GPS indices corresponding to corrected_gps_data
        output_filename: Output filename

    Returns:
        output_filename: Path to the exported file
    """
    print(f"\n=== EXPORTING CORRECTED GPS DATA ===")
    print(f"Output file: {output_filename}")
    print(f"Corrected GPS points: {len(corrected_gps_data)}")

    # Load original GPS data to get all columns
    original_gps_lines = []

    with open(gps_file_path, 'r') as f:
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 11:
                original_gps_lines.append(line.strip())

    # Create corrected GPS entries maintaining original structure
    corrected_entries = []

    for i, corrected_point in enumerate(corrected_gps_data):
        corrected_time = corrected_point[0]
        original_gps_idx = gps_indices[i]

        # Get the original line and replace the timestamp
        original_line = original_gps_lines[original_gps_idx]
        parts = original_line.split('\t')

        # Replace timestamp with corrected timestamp
        parts[0] = f"{corrected_time:.9f}"

        corrected_line = '\t'.join(parts)
        corrected_entries.append(corrected_line)

    # Write corrected GPS data with original structure
    with open(output_filename, 'w') as f:
        for entry in corrected_entries:
            f.write(f"{entry}\n")

    print(f"✅ Exported {len(corrected_entries)} corrected GPS points to: {output_filename}")
    print(f"Maintained original GPS file structure with corrected timestamps")
    print(f"Original GPS file remains unchanged")

    return output_filename

# Duplicate function removed - keeping the simpler version above

def synchronize_trajectories_improved(gps_data, baseline_data, time_tolerance=0.1):
    """
    Improved synchronization with 1:1 matching and detailed reporting.

    Args:
        gps_data: GPS data array with timestamps in first column
        baseline_data: Baseline data array with timestamps in first column
        time_tolerance: Maximum time difference for matching (seconds)

    Returns:
        tuple: (gps_sync, baseline_sync, sync_stats)
    """
    print(f"\n=== Trajectory Synchronization Analysis ===")
    print(f"GPS data: {len(gps_data)} points")
    print(f"Baseline data: {len(baseline_data)} points")
    print(f"Time tolerance: {time_tolerance} seconds")

    # Check time ranges
    gps_start, gps_end = gps_data[0, 0], gps_data[-1, 0]
    baseline_start, baseline_end = baseline_data[0, 0], baseline_data[-1, 0]

    print(f"GPS time range: {gps_start:.3f} to {gps_end:.3f} ({gps_end - gps_start:.1f}s)")
    print(f"Baseline time range: {baseline_start:.3f} to {baseline_end:.3f} ({baseline_end - baseline_start:.1f}s)")

    # Find overlapping time range
    overlap_start = max(gps_start, baseline_start)
    overlap_end = min(gps_end, baseline_end)
    print(f"Overlapping time range: {overlap_start:.3f} to {overlap_end:.3f} ({overlap_end - overlap_start:.1f}s)")

    # 1:1 matching algorithm
    gps_sync = []
    baseline_sync = []
    used_baseline_indices = set()
    time_errors = []

    # Sort both arrays by time (should already be sorted, but ensure it)
    gps_sorted_idx = np.argsort(gps_data[:, 0])
    baseline_sorted_idx = np.argsort(baseline_data[:, 0])

    matched_count = 0
    gps_unmatched = 0
    baseline_unmatched = 0

    for i in gps_sorted_idx:
        gps_time = gps_data[i, 0]

        # Skip GPS points outside overlap range
        if gps_time < overlap_start or gps_time > overlap_end:
            gps_unmatched += 1
            continue

        # Find closest unused baseline point
        best_idx = None
        best_time_diff = float('inf')

        for j in baseline_sorted_idx:
            if j in used_baseline_indices:
                continue

            baseline_time = baseline_data[j, 0]
            time_diff = abs(gps_time - baseline_time)

            if time_diff <= time_tolerance and time_diff < best_time_diff:
                best_time_diff = time_diff
                best_idx = j

        if best_idx is not None:
            gps_sync.append(gps_data[i])
            baseline_sync.append(baseline_data[best_idx])
            used_baseline_indices.add(best_idx)
            time_errors.append(best_time_diff)
            matched_count += 1
        else:
            gps_unmatched += 1

    # Count unmatched baseline points
    baseline_unmatched = len(baseline_data) - len(used_baseline_indices)

    # Convert to arrays
    gps_sync = np.array(gps_sync) if gps_sync else np.empty((0, gps_data.shape[1]))
    baseline_sync = np.array(baseline_sync) if baseline_sync else np.empty((0, baseline_data.shape[1]))
    time_errors = np.array(time_errors)

    # Synchronization statistics
    sync_stats = {
        'matched_pairs': matched_count,
        'gps_unmatched': gps_unmatched,
        'baseline_unmatched': baseline_unmatched,
        'time_errors': time_errors,
        'mean_time_error': np.mean(time_errors) if len(time_errors) > 0 else 0,
        'max_time_error': np.max(time_errors) if len(time_errors) > 0 else 0,
        'std_time_error': np.std(time_errors) if len(time_errors) > 0 else 0
    }

    # Report synchronization results
    print(f"\n=== Synchronization Results ===")
    print(f"Matched pairs: {matched_count}")
    print(f"GPS unmatched: {gps_unmatched}")
    print(f"Baseline unmatched: {baseline_unmatched}")
    print(f"Match rate: {matched_count / len(gps_data) * 100:.1f}% of GPS points")

    if len(time_errors) > 0:
        print(f"\n=== Time Synchronization Quality ===")
        print(f"Mean time error: {sync_stats['mean_time_error']:.6f} seconds")
        print(f"Max time error: {sync_stats['max_time_error']:.6f} seconds")
        print(f"Std time error: {sync_stats['std_time_error']:.6f} seconds")
        print(f"Points with >0.05s error: {np.sum(time_errors > 0.05)}")
        print(f"Points with >0.1s error: {np.sum(time_errors > 0.1)}")
    else:
        print("WARNING: No points were successfully synchronized!")

    return gps_sync, baseline_sync, sync_stats

def find_best_alignment(gps_coords, baseline_coords):
    """
    Find the best rotation, translation, and scaling to align trajectories.
    This handles rotation, flip, and translation issues.
    """
    # Center both trajectories
    gps_center = np.mean(gps_coords, axis=0)
    baseline_center = np.mean(baseline_coords, axis=0)

    gps_centered = gps_coords - gps_center
    baseline_centered = baseline_coords - baseline_center

    # Try different transformations to find the best alignment
    best_error = float('inf')
    best_transform = None
    best_baseline_transformed = None

    # Test different combinations of rotations and flips
    angles = np.linspace(0, 2*np.pi, 36)  # Test 36 different rotations
    flips = [(1, 1), (1, -1), (-1, 1), (-1, -1)]  # Test x/y flips

    for flip_x, flip_y in flips:
        for angle in angles:
            # Apply flip
            baseline_flipped = baseline_centered.copy()
            baseline_flipped[:, 0] *= flip_x
            baseline_flipped[:, 1] *= flip_y

            # Apply rotation
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation_matrix = np.array([[cos_a, -sin_a], [sin_a, cos_a]])
            baseline_rotated = baseline_flipped @ rotation_matrix.T

            # Calculate error (sum of squared distances)
            if len(baseline_rotated) == len(gps_centered):
                error = np.sum((gps_centered - baseline_rotated)**2)
                if error < best_error:
                    best_error = error
                    best_transform = (flip_x, flip_y, angle)
                    best_baseline_transformed = baseline_rotated + gps_center

    return best_baseline_transformed, best_transform, best_error

def select_files_gui():
    """Simple GUI to select GPS and baseline files."""
    root = tk.Tk()
    root.withdraw()  # Hide the main window

    # Get the directory where this script is located
    script_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(script_dir)

    print("=== FILE SELECTION GUI ===")
    print("Please select the GPS and baseline files...")

    # Select GPS file
    messagebox.showinfo("File Selection", "Please select the GPS data file (usually gps.txt)")
    gps_file = filedialog.askopenfilename(
        title="Select GPS Data File",
        initialdir=parent_dir,
        filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
    )

    if not gps_file:
        messagebox.showerror("Error", "No GPS file selected. Exiting.")
        root.destroy()
        return None, None

    # Select baseline file
    messagebox.showinfo("File Selection", "Please select the baseline data file (usually baseline.txt)")
    baseline_file = filedialog.askopenfilename(
        title="Select Baseline Data File",
        initialdir=parent_dir,
        filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
    )

    if not baseline_file:
        messagebox.showerror("Error", "No baseline file selected. Exiting.")
        root.destroy()
        return None, None

    root.destroy()

    print(f"Selected GPS file: {gps_file}")
    print(f"Selected baseline file: {baseline_file}")

    return gps_file, baseline_file

def analyze_and_plot_trajectories():
    """Main function to analyze and plot trajectories."""

    # Use GUI to select files
    gps_file, baseline_file = select_files_gui()

    if gps_file is None or baseline_file is None:
        print("File selection cancelled. Exiting.")
        return

    print("\nLoading GPS and Baseline data...")
    print(f"GPS file: {gps_file}")
    print(f"Baseline file: {baseline_file}")

    # Get script directory for saving plots
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # Load the selected files
    gps_data = load_gps_data(gps_file)
    baseline_data = load_baseline_data(baseline_file)

    print(f"Loaded {len(gps_data)} GPS points and {len(baseline_data)} baseline points")

    # Analyze coordinate ranges
    print("\n=== Coordinate System Analysis ===")
    lat_range = [np.min(gps_data[:, 1]), np.max(gps_data[:, 1])]
    lon_range = [np.min(gps_data[:, 2]), np.max(gps_data[:, 2])]
    x_range = [np.min(baseline_data[:, 1]), np.max(baseline_data[:, 1])]
    y_range = [np.min(baseline_data[:, 2]), np.max(baseline_data[:, 2])]

    print(f"GPS Latitude range: {lat_range[0]:.6f}° to {lat_range[1]:.6f}°")
    print(f"GPS Longitude range: {lon_range[0]:.6f}° to {lon_range[1]:.6f}°")
    print(f"Baseline X range: {x_range[0]:.1f} to {x_range[1]:.1f} m")
    print(f"Baseline Y range: {y_range[0]:.1f} to {y_range[1]:.1f} m")

    print(f"\nBased on coordinates (~{x_range[0]/1e6:.1f}M, ~{y_range[0]/1e3:.0f}K),")
    print("the baseline appears to be in UTM Zone 52N projection.")

    # Convert GPS to UTM Zone 52N
    print("\nConverting GPS to UTM Zone 52N coordinates...")
    gps_utm = lat_lon_to_utm_52n(gps_data)

    # Convert GPS UTM to baseline coordinate system
    print("Converting GPS to baseline coordinate system...")
    gps_baseline_coords = convert_gps_to_baseline_coordinate_system(gps_utm)

    # SPATIAL MATCHING APPROACH: Assign baseline timestamps to GPS points
    # This corrects GPS timestamps by finding spatially nearest baseline points
    print("\n=== SPATIAL TIMESTAMP CORRECTION ===")
    corrected_gps_data, gps_indices, matching_stats = correct_gps_timestamps_by_spatial_matching(gps_baseline_coords, baseline_data)

    # Export corrected GPS data to new file
    export_corrected_gps_data(corrected_gps_data, gps_indices, gps_file, "gps_corrected.txt")

    # Since GPS points now have baseline timestamps, synchronization should be perfect
    gps_sync, baseline_sync, sync_stats = synchronize_trajectories_improved(corrected_gps_data, baseline_data)

    if len(gps_sync) == 0:
        print("ERROR: No synchronized points found! Check time ranges and tolerance.")
        return

    # Extract coordinates for ERROR CALCULATION (synchronized data only)
    gps_coords_sync = gps_sync[:, 1:3]
    baseline_coords_sync = baseline_sync[:, 1:3]

    # Extract coordinates for PLOTTING (full original data)
    gps_coords_full = gps_baseline_coords[:, 1:3]  # Original GPS coordinates
    baseline_coords_full = baseline_data[:, 1:3]

    # Simple alignment by centering (using synchronized data for alignment calculation)
    print("\nApplying simple translation alignment...")
    gps_center = np.mean(gps_coords_sync, axis=0)
    baseline_center = np.mean(baseline_coords_sync, axis=0)
    translation = gps_center - baseline_center

    # Apply translation to both synchronized and full datasets
    baseline_aligned_sync = baseline_coords_sync + translation
    baseline_aligned_full = baseline_coords_full + translation

    print(f"Applied translation: {translation}")
    print("Note: Both trajectories are now in the same coordinate system (baseline system)")

    # Convert to local coordinates for better visualization (subtract origin)
    print("\nConverting to local coordinates for visualization...")
    # Use the first GPS point as the local origin
    local_origin = gps_coords_full[0, :]

    # Convert all coordinates to local system (relative to origin)
    gps_coords_full_local = gps_coords_full - local_origin
    baseline_aligned_full_local = baseline_aligned_full - local_origin
    gps_coords_sync_local = gps_coords_sync - local_origin
    baseline_aligned_sync_local = baseline_aligned_sync - local_origin

    print(f"Local origin set to: ({local_origin[0]:.1f}, {local_origin[1]:.1f}) m")
    print(f"All coordinates now relative to this origin for better readability")

    # Calculate position errors once and reuse (using synchronized data only)
    position_errors = np.sqrt(np.sum((gps_coords_sync - baseline_aligned_sync)**2, axis=1))

    # Error statistics
    error_stats = {
        'mean': np.mean(position_errors),
        'std': np.std(position_errors),
        'min': np.min(position_errors),
        'max': np.max(position_errors),
        'rmse': np.sqrt(np.mean(position_errors**2)),
        'median': np.median(position_errors)
    }
    
    # Create single large plot with both trajectories
    plt.figure(figsize=(16, 12))

    # Plot both FULL trajectories in the same plot (using local coordinates)
    plt.plot(gps_coords_full_local[:, 0], gps_coords_full_local[:, 1], 'b-', linewidth=2, label='GPS Trajectory (Full)', marker='.' , alpha=0.8)
    plt.plot(baseline_aligned_full_local[:, 0], baseline_aligned_full_local[:, 1], 'r-', linewidth=2, label='Baseline (Ground Truth, Full)', marker='.', alpha=0.8)

    # Mark start and end points with larger markers (using local coordinates)
    plt.plot(gps_coords_full_local[0, 0], gps_coords_full_local[0, 1], 'bo', markersize=12, label='GPS Start', markeredgecolor='darkblue', markeredgewidth=2)
    plt.plot(gps_coords_full_local[-1, 0], gps_coords_full_local[-1, 1], 'bs', markersize=12, label='GPS End', markeredgecolor='darkblue', markeredgewidth=2)
    plt.plot(baseline_aligned_full_local[0, 0], baseline_aligned_full_local[0, 1], 'ro', markersize=12, label='Baseline Start', markeredgecolor='darkred', markeredgewidth=2)
    plt.plot(baseline_aligned_full_local[-1, 0], baseline_aligned_full_local[-1, 1], 'rs', markersize=12, label='Baseline End', markeredgecolor='darkred', markeredgewidth=2)

    # Add some trajectory points for better visualization
    # step = len(gps_coords) // 20  # Show every 20th point
    # plt.plot(gps_coords[::step, 0], gps_coords[::step, 1], 'b.', markersize=10, alpha=0.6)
    # plt.plot(baseline_aligned[::step, 0], baseline_aligned[::step, 1], 'r.', markersize=10, alpha=0.6)

    # Highlight synchronized timestamp pairs to demonstrate time alignment (using synchronized data)
    print(f"\nAdding synchronized timestamp pairs to demonstrate time alignment...")
    print(f"Available synchronized points: {len(gps_coords_sync)}")
    if len(gps_coords_sync) >= 10:  # Reduced threshold to ensure we show pairs
        # Select random points for visualization (like before)
        num_pairs = min(100, len(gps_coords_sync))  # Show up to 100 pairs
        np.random.seed(42)  # For reproducible results
        selected_indices = np.random.choice(len(gps_coords_sync), size=num_pairs, replace=False)
        print(f"Showing {len(selected_indices)} randomly selected synchronized pairs on the plot")

        # Get the corresponding coordinates and timestamps (convert to local coordinates)
        selected_gps_coords = gps_coords_sync_local[selected_indices]
        selected_baseline_coords = baseline_aligned_sync_local[selected_indices]
        selected_gps_times = gps_sync[selected_indices, 0]
        selected_baseline_times = baseline_sync[selected_indices, 0]

        # Plot connecting lines between synchronized pairs (make them more visible)
        for i in range(len(selected_indices)):
            # Draw a line connecting GPS and baseline points at the same timestamp
            plt.plot([selected_gps_coords[i, 0], selected_baseline_coords[i, 0]],
                    [selected_gps_coords[i, 1], selected_baseline_coords[i, 1]],
                    'purple', alpha=0.8, linewidth=10.0, zorder=9)

            # Add timestamp labels for some points
            if i % 3 == 0:  # Label every 3rd pair
                time_relative = (selected_gps_times[i] - selected_gps_times[0]) / 60  # Minutes
                plt.annotate(f'{time_relative:.1f}min',
                           ((selected_gps_coords[i, 0] + selected_baseline_coords[i, 0])/2,
                            (selected_gps_coords[i, 1] + selected_baseline_coords[i, 1])/2),
                           fontsize=20, color='purple', alpha=0.8, ha='center')

        # Highlight the selected GPS points (make them more visible)
        plt.scatter(selected_gps_coords[:, 0], selected_gps_coords[:, 1],
                   c='cyan', s=80, alpha=1.0, edgecolors='darkblue', linewidth=2,
                   label=f'Synchronized GPS Points ({num_pairs})', zorder=10)

        # Highlight the corresponding baseline points (make them more visible)
        plt.scatter(selected_baseline_coords[:, 0], selected_baseline_coords[:, 1],
                   c='yellow', s=80, alpha=1.0, edgecolors='darkred', linewidth=2,
                   label=f'Corresponding Baseline Points ({num_pairs})', zorder=10)

        # Calculate and report time differences for the selected sample
        time_diffs = np.abs(selected_gps_times - selected_baseline_times)
        print(f"Time synchronization quality for {num_pairs} evenly distributed pairs:")
        print(f"  Mean time difference: {np.mean(time_diffs):.6f} seconds")
        print(f"  Max time difference: {np.max(time_diffs):.6f} seconds")
        print(f"  Points with >0.01s difference: {np.sum(time_diffs > 0.01)}")

        # Calculate spatial distances between synchronized pairs
        spatial_distances = np.sqrt(np.sum((selected_gps_coords - selected_baseline_coords)**2, axis=1))
        print(f"Spatial distances for {num_pairs} synchronized pairs:")
        print(f"  Mean distance: {np.mean(spatial_distances):.3f} m")
        print(f"  Max distance: {np.max(spatial_distances):.3f} m")
        print(f"  Min distance: {np.min(spatial_distances):.3f} m")

        # Check if trajectories are properly aligned by looking at the pattern
        print(f"\n=== Trajectory Alignment Analysis ===")
        print(f"If trajectories are properly time-synchronized, the connecting lines should be:")
        print(f"- Short (small spatial distances)")
        print(f"- Roughly perpendicular to trajectory direction")
        print(f"- Not systematically pointing in one direction")
    else:
        print(f"Not enough synchronized points ({len(gps_coords_sync)}) to show timestamp pairs")

    plt.xlabel('Local X Coordinate (m)', fontsize=14)
    plt.ylabel('Local Y Coordinate (m)', fontsize=14)
    plt.title('GPS vs Baseline Trajectories Comparison (Local Coordinates)\n(1:1 Time Synchronization + 100 Random Timestamp Pairs Highlighted)', fontsize=16, fontweight='bold')
    plt.legend(fontsize=10, loc='best', ncol=2)
    plt.grid(True, alpha=0.3)
    plt.axis('equal')

    # Add error statistics and origin info as text on the plot
    stats_text = f'RMSE: {error_stats["rmse"]:.2f} m\nMean Error: {error_stats["mean"]:.2f} m\nMax Error: {error_stats["max"]:.2f} m\nSynchronized Points: {len(gps_coords_sync)}\nFull GPS Points: {len(gps_coords_full_local)}\n\nLocal Origin:\n({local_origin[0]:.1f}, {local_origin[1]:.1f}) m'
    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, fontsize=12,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    plt.tight_layout()

    # Save plot in the same directory as the script
    plot_file = os.path.join(script_dir, 'trajectory_comparison_with_timestamp_validation.png')
    plt.savefig(plot_file, dpi=300, bbox_inches='tight')
    print(f"\nPlot saved as '{plot_file}'")

    # Print comprehensive error statistics
    print(f"\n=== Position Error Statistics ===")
    print(f"Synchronized points: {len(position_errors)}")
    print(f"Mean Error: {error_stats['mean']:.3f} m")
    print(f"Std Error: {error_stats['std']:.3f} m")
    print(f"Min Error: {error_stats['min']:.3f} m")
    print(f"Max Error: {error_stats['max']:.3f} m")
    print(f"Median Error: {error_stats['median']:.3f} m")
    print(f"RMSE: {error_stats['rmse']:.3f} m")

    # Plot error over time
    plt.figure(figsize=(12, 6))
    time_sync = gps_sync[:len(position_errors), 0]
    time_relative = (time_sync - time_sync[0]) / 60  # Convert to minutes

    plt.plot(time_relative, position_errors, 'g-', linewidth=1)
    plt.xlabel('Time (minutes)')
    plt.ylabel('Position Error (m)')
    plt.title('GPS vs Baseline Position Error Over Time\n(Improved 1:1 Synchronization)')
    plt.grid(True, alpha=0.3)

    # Add statistics text on the plot
    stats_text = f'RMSE: {error_stats["rmse"]:.2f}m\nMean: {error_stats["mean"]:.2f}m\nPoints: {len(position_errors)}'
    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, fontsize=10,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

    plt.tight_layout()
    error_plot_file = os.path.join(script_dir, 'position_errors_improved_sync.png')
    plt.savefig(error_plot_file, dpi=300, bbox_inches='tight')
    print(f"Error plot saved as '{error_plot_file}'")

    # Print comprehensive summary
    print(f"\n=== Analysis Summary ===")
    print(f"GPS Data: WGS84 Lat/Lon → UTM Zone 52N → Baseline Coordinate System")
    print(f"Baseline Data: Already in Baseline Coordinate System")
    print(f"Transformation Applied: UTM_Easting → Baseline_Y, UTM_Northing → Baseline_X")
    print(f"Translation Applied: {translation}")
    print(f"Timestamp Correction: Spatial matching (GPS timestamps replaced with baseline timestamps)")
    print(f"Synchronization: 1:1 matching with {sync_stats['matched_pairs']} pairs")
    print(f"Time sync quality: {sync_stats['mean_time_error']:.6f}s mean error")
    print(f"Final RMSE: {error_stats['rmse']:.3f} m")

    plt.show()

if __name__ == "__main__":
    analyze_and_plot_trajectories()
